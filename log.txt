2025-07-30 10:50:56.009388: 61
2025-07-30 10:50:56.012379: getDBPath : C:\Users\<USER>\AppData\Roaming\com.example\a3g\a3g_setting.db
2025-07-30 10:50:56.235679: socket 连接成功,isBroadcast:false
2025-07-30 10:50:56.236680: changeSocketStatus:true
2025-07-30 10:50:56.236680: Sip2HeartBeatManager start loginACS:false askACS:false
2025-07-30 10:50:56.236680: Req msgType：Sip2MsgType.login ,length:72， ret:  9300CNhlsp_sip2|COsip2|CP3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AY1AZEC16
2025-07-30 10:50:56.259920: 已添加配置变化监听器
2025-07-30 10:50:56.259920: 认证优先级管理器: 开始加载认证方式
2025-07-30 10:50:56.260896: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-07-30 10:50:56.260896: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 10:50:56.260896: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-07-30 10:50:56.260896: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-07-30 10:50:56.260896: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-07-30 10:50:56.260896: 认证优先级管理器: 最终排序结果: 人脸识别 -> 读者证 -> 社保卡
2025-07-30 10:50:56.260896: 认证优先级管理器: 主要认证方式: 人脸识别
2025-07-30 10:50:56.260896: 更新认证方式显示文字: 人脸识别/读者证/社保卡
2025-07-30 10:50:56.303926: Rsp : 941AY1AZFDFC
2025-07-30 10:50:56.310342: loginRsp:{OK: 1, MsgSeqId: 1AZFDFC}
2025-07-30 10:50:56.311160: Sip2HeartBeatManager start loginACS:false askACS:true
2025-07-30 10:50:56.311160: 发送心跳
2025-07-30 10:50:56.311505: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY2AZFC9F
2025-07-30 10:50:56.374081: Rsp : 98YYYNNN00500320250730    1052222.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY2AZD52B
2025-07-30 10:50:56.614403: dispose IndexPage
2025-07-30 10:50:56.615368: IndexPage dispose
2025-07-30 10:51:06.572287: 手动触发跳转到认证页面
2025-07-30 10:51:06.592341: AuthView初始化 - 主认证方式: 人脸识别
2025-07-30 10:51:06.592341: 尝试初始化多认证系统
2025-07-30 10:51:06.592341: 多认证管理器状态变更: initializing
2025-07-30 10:51:06.592341: 认证优先级管理器: 开始加载认证方式
2025-07-30 10:51:06.592341: 配置的排序: [人脸识别认证, 微信扫码认证, 读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 社保卡认证, 市民卡认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 微信二维码认证, 上海随申码认证, 汇文二维码, 支付宝二维码认证, 支付宝二维码认证（阿里信用）]
2025-07-30 10:51:06.592341: 可用的认证方式: [读者证认证, 身份证认证, 腾讯E证通认证, 手动输入认证, 人脸识别认证, 社保卡认证, 市民卡认证, 微信二维码认证, 借阅宝认证, 支付宝扫码认证, 芝麻信用码认证, 支付宝扫码认证（阿里信用）, 微信/支付宝认证, 二维码读者认证, IMI身份认证, 电子社保卡认证, 拍照配置, 上海随申码认证, 微信扫码认证, 汇文二维码]
2025-07-30 10:51:06.592341: 认证优先级管理器: 按配置顺序添加 人脸识别认证 -> 人脸识别
2025-07-30 10:51:06.593318: 认证优先级管理器: 按配置顺序添加 读者证认证 -> 读者证
2025-07-30 10:51:06.593318: 认证优先级管理器: 按配置顺序添加 社保卡认证 -> 社保卡
2025-07-30 10:51:06.593318: 认证优先级管理器: 最终排序结果: 人脸识别 -> 读者证 -> 社保卡
2025-07-30 10:51:06.593318: 认证优先级管理器: 主要认证方式: 人脸识别
2025-07-30 10:51:06.593318: 多认证管理器: 从优先级管理器加载的认证方式: 人脸识别 -> 读者证 -> 社保卡
2025-07-30 10:51:06.593318: 多认证管理器: 当前默认显示方式: 人脸识别
2025-07-30 10:51:06.593318: 初始化人脸认证服务
2025-07-30 10:51:06.593318: 📷 开始人脸识别认证服务初始化流程...
2025-07-30 10:51:06.593318: 🔧 第一步：初始化摄像头系统...
2025-07-30 10:51:06.593318: 📷 检查人脸检测器初始化状态...
2025-07-30 10:51:06.593318: 🔧 开始初始化人脸检测器...
2025-07-30 10:51:06.594315: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\libface_detector.dll
2025-07-30 10:51:06.594315: 文件不存在: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\libface_detector.dll
2025-07-30 10:51:06.594315: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\data\libface_detector.dll
2025-07-30 10:51:06.594315: 文件不存在: D:\gdwork\code\a3g\build\windows\runner\Release\windows\runner\Debug\data\libface_detector.dll
2025-07-30 10:51:06.594315: 尝试加载库: D:\gdwork\code\a3g\build\windows\runner\Release\libface_detector.dll
2025-07-30 10:51:06.594315: 成功加载库: D:\gdwork\code\a3g\build\windows\runner\Release\libface_detector.dll
2025-07-30 10:51:06.594315: 成功加载系统负载和处理间隔函数
2025-07-30 10:51:08.810213: 开始初始化门锁连接...
2025-07-30 10:51:08.811211: 创建门锁连接: 主门锁设备 (COM1)
2025-07-30 10:51:08.811211: 门锁继电器数据监听已启动
2025-07-30 10:51:08.811211: 门锁继电器连接成功: COM1
2025-07-30 10:51:08.811211: 设置认证方法: AuthMethod.face, 当前方法: AuthMethod.face
2025-07-30 10:51:08.811211: ✅ 人脸检测器初始化完成，耗时: 11ms
2025-07-30 10:51:08.811211: 🔍 检测可用摄像头设备...
2025-07-30 10:51:08.812221: ✅ 检测到 1 个摄像头设备: [0]
2025-07-30 10:51:08.812221: 📹 启动摄像头（带重试机制）...
2025-07-30 10:51:08.812221: 📹 尝试使用摄像头ID: 0
2025-07-30 10:51:08.812221: 📹 摄像头启动尝试 1/2，ID: 0
2025-07-30 10:51:08.812221: 门锁连接成功: COM1
2025-07-30 10:51:08.812725: 门锁连接初始化完成，共建立 1 个连接
2025-07-30 10:51:08.812725: changeReaders
2025-07-30 10:51:08.812725: createIsolate isOpen:false,isOpening:false
2025-07-30 10:51:08.812725: ✅ 摄像头启动成功，ID: 0，尝试次数: 1，耗时: 1367ms，后端: DirectShow
2025-07-30 10:51:08.812725: 🔧 配置摄像头参数...
2025-07-30 10:51:08.812725: ✅ 摄像头参数配置完成
2025-07-30 10:51:08.812725: 🤖 预加载人脸检测模型...
2025-07-30 10:51:08.812725: 预加载DNN模型...
2025-07-30 10:51:08.812725: DNN模型预加载完成，获取到 0 个结果
2025-07-30 10:51:08.812725: ✅ 人脸检测模型预加载完成
2025-07-30 10:51:08.812725: 🔍 验证摄像头功能...
2025-07-30 10:51:08.813725: ⚠️ 摄像头功能验证失败，但继续执行
2025-07-30 10:51:08.813725: ✅ 人脸检测器初始化完成
2025-07-30 10:51:08.813725: ✅ 摄像头系统初始化成功
2025-07-30 10:51:08.813725: 🔧 第二步：初始化百度人脸识别SDK...
2025-07-30 10:51:08.814475: 配置文件不存在，使用默认配置: D:\gdwork\code\a3g\build\windows\runner\Release\sdk_config.json
2025-07-30 10:51:08.814475: 从配置获取SDK路径: D:\FaceOfflineSdk
2025-07-30 10:51:15.650210: ✅ SDK路径验证成功
2025-07-30 10:51:15.651216: 🔧 配置SDK路径: D:\FaceOfflineSdk
2025-07-30 10:51:15.651216: SDK未初始化，无法设置路径
2025-07-30 10:51:15.651216: ✅ SDK路径配置完成，错误码: -100
2025-07-30 10:51:15.651216: 🚀 开始初始化百度人脸识别SDK...
2025-07-30 10:51:15.651216: 百度SDK初始化成功, 人脸数据库加载结果: 1
2025-07-30 10:51:15.652205: ✅ 百度人脸识别SDK初始化成功
2025-07-30 10:51:15.652205: ✅ 百度SDK状态: 已初始化，人脸数量: 4，授权状态: 已授权
2025-07-30 10:51:15.652205: ✅ 百度人脸识别SDK初始化成功
2025-07-30 10:51:15.652205: 🔧 第三步：重新创建认证结果流控制器...
2025-07-30 10:51:15.652205: ✅ 认证结果流控制器重新创建成功
2025-07-30 10:51:15.653202: 🔧 第四步：启动系统监控...
2025-07-30 10:51:15.653202: ✅ 系统监控启动成功
2025-07-30 10:51:15.653202: ✅ 人脸识别认证服务初始化完成
2025-07-30 10:51:15.653202: 人脸认证服务初始化成功
2025-07-30 10:51:15.653202: 初始化人脸认证服务
2025-07-30 10:51:15.653202: 人脸识别 认证服务初始化成功
2025-07-30 10:51:15.653202: 初始化读卡器认证服务
2025-07-30 10:51:15.653202: 读卡器认证服务初始化成功
2025-07-30 10:51:15.653202: 初始化共享读卡器认证服务
2025-07-30 10:51:15.654198: 读者证 认证服务初始化成功
2025-07-30 10:51:15.654198: 社保卡 认证服务初始化成功
2025-07-30 10:51:15.654198: 认证服务初始化完成，共初始化 3 种认证方式
2025-07-30 10:51:15.654198: 多认证管理器状态变更: idle
2025-07-30 10:51:15.654198: 多认证管理器初始化完成，启用的认证方式: [AuthMethod.face, AuthMethod.readerCard, AuthMethod.socialSecurityCard]
2025-07-30 10:51:15.654198: 检测到多种认证方式(3种)，启用多认证模式
2025-07-30 10:51:15.654198: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:15.654198: 多认证管理器状态变更: listening
2025-07-30 10:51:15.654198: 启动所有认证方式监听: [AuthMethod.face, AuthMethod.readerCard, AuthMethod.socialSecurityCard]
2025-07-30 10:51:15.654198: 准备启动 2 个物理认证服务
2025-07-30 10:51:15.654198: 开始人脸认证监听
2025-07-30 10:51:15.654198: 🚀 开始人脸识别监听（使用FaceCapturePolling方式）...
2025-07-30 10:51:15.655196: 🆕 创建新的人脸捕获轮询服务...
2025-07-30 10:51:15.655196: ✅ 人脸捕获轮询服务初始化成功
2025-07-30 10:51:15.655196: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:15.655196: ✅ 人脸识别监听启动成功
2025-07-30 10:51:15.655196: 人脸认证监听启动成功
2025-07-30 10:51:15.655196: 人脸识别 认证服务启动成功
2025-07-30 10:51:15.655196: 开始读卡器认证监听
2025-07-30 10:51:15.655196: 强制重新配置读卡器以确保状态一致性
2025-07-30 10:51:15.655196: 完全重置读卡器连接和监听器状态...
2025-07-30 10:51:15.655196: 已移除读卡器状态监听器
2025-07-30 10:51:15.655196: 已移除标签数据监听器
2025-07-30 10:51:15.656193: 所有卡片监听器已移除
2025-07-30 10:51:15.656193: stopInventory newPort:null
2025-07-30 10:51:15.656193: createIsolate newport null
2025-07-30 10:51:15.857548: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:15.857548: 读卡器连接已完全关闭
2025-07-30 10:51:15.858548: 读卡器连接和监听器状态已完全重置
2025-07-30 10:51:15.858548: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 10:51:15.858548: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 10:51:15.858548: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 10:51:15.858548: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-07-30 10:51:15.859543: 添加有效设备: type=10, id=10
2025-07-30 10:51:15.859543: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 10:51:15.859543: 添加有效设备: type=2, id=2
2025-07-30 10:51:15.859543: 验证读卡器配置: 类型=13, 解码器=不解析
2025-07-30 10:51:15.859543: 添加有效设备: type=13, id=13
2025-07-30 10:51:15.859543: 总共加载了3个设备配置
2025-07-30 10:51:15.859543: changeReaders
2025-07-30 10:51:15.859543: createIsolate isOpen:false,isOpening:true
2025-07-30 10:51:15.859543: open():SendPort
2025-07-30 10:51:15.860541: untilDetcted():SendPort
2025-07-30 10:51:15.860541: 读卡器配置完成，共 3 个设备
2025-07-30 10:51:15.860541: 已移除读卡器状态监听器
2025-07-30 10:51:15.860541: 已移除标签数据监听器
2025-07-30 10:51:15.860541: 所有卡片监听器已移除
2025-07-30 10:51:15.860541: 已添加读卡器状态监听器
2025-07-30 10:51:15.860541: 已添加标签数据监听器
2025-07-30 10:51:15.860541: 开始监听卡片数据 - 所有监听器已就绪
2025-07-30 10:51:15.860541: 读卡器认证监听启动成功
2025-07-30 10:51:15.861537: 读者证、社保卡 认证服务启动成功
2025-07-30 10:51:15.861537: 所有认证服务启动完成，成功启动 2 个服务
2025-07-30 10:51:15.861537: 当前可用的认证方式: 人脸识别、读者证、社保卡
2025-07-30 10:51:15.861537: 多认证系统初始化完成，正在同时监听以下认证方式: 人脸识别、读者证、社保卡
2025-07-30 10:51:15.861537: subThread :ReaderCommand.close
2025-07-30 10:51:15.862535: commandRsp:ReaderCommand.close
2025-07-30 10:51:15.862535: cacheUsedReaders:()
2025-07-30 10:51:15.862535: close:done
2025-07-30 10:51:15.863297: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:15.863297: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:15.863297: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:15.863297: already close all reader
2025-07-30 10:51:15.864311: subThread :ReaderCommand.readerList
2025-07-30 10:51:15.864311: commandRsp:ReaderCommand.readerList
2025-07-30 10:51:15.864311: readerList：3,readerSetting：3
2025-07-30 10:51:15.864311: cacheUsedReaders:3
2025-07-30 10:51:15.864311: subThread :ReaderCommand.open
2025-07-30 10:51:15.864311: commandRsp:ReaderCommand.open
2025-07-30 10:51:17.570457: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:17.570457: === 构建多认证底部组件 ===
2025-07-30 10:51:17.570457: 显示底部组件: false
2025-07-30 10:51:17.570457: 反馈状态: AuthFeedbackState.idle
2025-07-30 10:51:17.570457: 用户信息: null (null)
2025-07-30 10:51:17.570457: 底部组件被隐藏，返回空组件
2025-07-30 10:51:17.570457: 📷 CameraPreviewWidget: 检查摄像头检测器初始化状态...
2025-07-30 10:51:17.570457: ✅ CameraPreviewWidget: 摄像头检测器已初始化，跳过初始化步骤
2025-07-30 10:51:17.570457: 📹 CameraPreviewWidget: 启动摄像头 0...
2025-07-30 10:51:17.570457: CameraPreviewWidget: 开始初始化人脸识别服务...
2025-07-30 10:51:17.571455: 人脸识别认证服务已初始化
2025-07-30 10:51:17.571455: ✅ CameraPreviewWidget: 摄像头启动成功，耗时: 1705ms
2025-07-30 10:51:17.571455: CameraPreviewWidget: 预加载DNN模型...
2025-07-30 10:51:17.571455: CameraPreviewWidget: DNN模型预加载完成，获取到 0 个结果
2025-07-30 10:51:17.571455: 人脸识别已在监听中
2025-07-30 10:51:17.571455: CameraPreviewWidget: 帧捕获已启动，间隔: 60ms
2025-07-30 10:51:17.571455: CameraPreviewWidget: 人脸检测器已启动，检测间隔: 80ms
2025-07-30 10:51:17.571455: ✅ CameraPreviewWidget: 摄像头初始化完成
2025-07-30 10:51:17.571455: CameraPreviewWidget: 人脸识别服务初始化成功
2025-07-30 10:51:17.572452: dc_init:0xb4 100
2025-07-30 10:51:17.572452: open reader readerType ：10 ret：0
2025-07-30 10:51:17.572452: wsurl:172.16.1.103:9091
2025-07-30 10:51:17.572452: subThread :ReaderCommand.untilDetected
2025-07-30 10:51:17.572452: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:51:17.572452: open():SendPort
2025-07-30 10:51:17.572452: untilDetcted():SendPort
2025-07-30 10:51:17.573449: dc_config_card:0
2025-07-30 10:51:17.573449: dc_card_n_hex:1,len:0
2025-07-30 10:51:17.573449: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:17.573449: iReadCardBas ret:4294967294
2025-07-30 10:51:17.573449: 无卡
2025-07-30 10:51:17.573449: 无卡
2025-07-30 10:51:17.573449: dc_config_card:0
2025-07-30 10:51:17.573449: dc_card_n_hex:1,len:0
2025-07-30 10:51:17.573449: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:17.654246: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:17.654)
2025-07-30 10:51:17.654246: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 10:51:17.654)
2025-07-30 10:51:17.654246: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 10:51:17.868614: iReadCardBas ret:4294967294
2025-07-30 10:51:17.868614: 无卡
2025-07-30 10:51:17.868614: 无卡
2025-07-30 10:51:17.869636: subThread :ReaderCommand.readerList
2025-07-30 10:51:17.869636: commandRsp:ReaderCommand.readerList
2025-07-30 10:51:17.869636: readerList：1,readerSetting：1
2025-07-30 10:51:17.870140: cacheUsedReaders:3
2025-07-30 10:51:17.870140: subThread :ReaderCommand.open
2025-07-30 10:51:17.870140: commandRsp:ReaderCommand.open
2025-07-30 10:51:17.870140: wsurl:172.16.1.103:9091
2025-07-30 10:51:17.870140: subThread :ReaderCommand.untilDetected
2025-07-30 10:51:17.871139: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:51:19.654937: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:19.654)
2025-07-30 10:51:19.654937: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 10:51:19.654)
2025-07-30 10:51:19.654937: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 10:51:20.613538: AuthView: 检测到 1 个人脸
2025-07-30 10:51:20.693312: AuthView: 检测到 1 个人脸
2025-07-30 10:51:20.772958: AuthView: 检测到 1 个人脸
2025-07-30 10:51:20.934752: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.012990: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.093325: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.173816: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.253377: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.332567: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.414065: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.493335: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.573510: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.653976: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.654974: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:21.653)
2025-07-30 10:51:21.655984: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:21.653)
2025-07-30 10:51:21.655984: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:21.655984: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:21.655984: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:21.655984: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 144.3317108154297, 数据大小: 0 bytes
2025-07-30 10:51:21.655984: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:21.655984: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:21.655984: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4171 bytes, 实际置信度: 144.3317108154297
2025-07-30 10:51:21.655984: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:21.656967: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:21.656967: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:21.656967: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:21.656967: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:21.894012: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:21.894012: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "3451c21ecd7afd60db6df2598642133f",
		"log_id" : "1753843881894",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 93.23,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:21.894012: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:21.894012: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=93.23
2025-07-30 10:51:21.895010: 人脸识别成功，得分: 93.23，用户ID: 111111
2025-07-30 10:51:21.895010: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 10:51:21.895010: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-07-30 10:51:21.895010: 🔍 开始请求读者信息: 用户ID=111111
2025-07-30 10:51:21.895010: 63 CardType 值为空
2025-07-30 10:51:21.895010: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250730    105121  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY3AZF027
2025-07-30 10:51:21.897003: AuthView: 检测到 1 个人脸
2025-07-30 10:51:21.972261: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.053580: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.134664: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.212882: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.277835: Rsp : 64              00120250730    105248000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY3AZDBBD
2025-07-30 10:51:22.292798: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105248, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBD}
2025-07-30 10:51:22.292798: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105248, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 3AZDBBD}
2025-07-30 10:51:22.292798: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-07-30 10:51:22.292798: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-07-30 10:51:22.292798: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.293796: 多认证管理器: 收到认证结果: 人脸识别 - success
2025-07-30 10:51:22.293796: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:22.293796: 多认证管理器状态变更: authenticating
2025-07-30 10:51:22.293796: 多认证管理器: 认证成功，立即停止其他认证方式
2025-07-30 10:51:22.293796: 停止读卡器认证监听
2025-07-30 10:51:22.293796: 停止读卡器认证监听
2025-07-30 10:51:22.293796: 已发起停止其他认证方式的操作
2025-07-30 10:51:22.293796: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:22.293796: 多认证管理器状态变更: completed
2025-07-30 10:51:22.294793: 已移除读卡器状态监听器
2025-07-30 10:51:22.294793: 已移除标签数据监听器
2025-07-30 10:51:22.294793: 所有卡片监听器已移除
2025-07-30 10:51:22.294793: 没有活跃的读卡器连接需要暂停
2025-07-30 10:51:22.294793: 已移除读卡器状态监听器
2025-07-30 10:51:22.294793: 已移除标签数据监听器
2025-07-30 10:51:22.294793: 所有卡片监听器已移除
2025-07-30 10:51:22.294793: 没有活跃的读卡器连接需要暂停
2025-07-30 10:51:22.294793: 收到多认证结果: 人脸识别 - success
2025-07-30 10:51:22.294793: 认证成功，更新主显示方式为: 人脸识别
2025-07-30 10:51:22.295790: === 更新认证反馈状态 ===
2025-07-30 10:51:22.295790: 结果状态: AuthStatus.success
2025-07-30 10:51:22.295790: 用户姓名: gd
2025-07-30 10:51:22.295790: 用户ID: 111111
2025-07-30 10:51:22.295790: 设置反馈状态为success，显示底部组件: true
2025-07-30 10:51:22.295790: 反馈状态: AuthFeedbackState.success
2025-07-30 10:51:22.295790: 用户信息: gd (111111)
2025-07-30 10:51:22.295790: 读卡器认证监听已停止（连接保持）
2025-07-30 10:51:22.295790: 已停止 readerCard 认证监听
2025-07-30 10:51:22.295790: 读卡器认证监听已停止（连接保持）
2025-07-30 10:51:22.295790: 已停止 socialSecurityCard 认证监听
2025-07-30 10:51:22.301591: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:22.301591: === 构建多认证底部组件 ===
2025-07-30 10:51:22.301591: 显示底部组件: true
2025-07-30 10:51:22.301591: 反馈状态: AuthFeedbackState.success
2025-07-30 10:51:22.301591: 用户信息: gd (111111)
2025-07-30 10:51:22.301591: 创建AuthFeedbackWidget
2025-07-30 10:51:22.301591: 实际认证方式: AuthMethod.face
2025-07-30 10:51:22.301591: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:22.302592: 当前状态: AuthFeedbackState.success
2025-07-30 10:51:22.302592: 返回成功组件 (详细信息)
2025-07-30 10:51:22.302592: === 构建成功组件（详细信息） ===
2025-07-30 10:51:22.302592: 用户: gd, ID: 111111
2025-07-30 10:51:22.302592: === AuthFeedbackWidget 状态变化 ===
2025-07-30 10:51:22.302592: 新状态: AuthFeedbackState.success
2025-07-30 10:51:22.302592: 用户信息: gd (111111)
2025-07-30 10:51:22.302592: 启动定时器，3秒后切换到通行模式
2025-07-30 10:51:22.314109: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:22.315074: 当前状态: AuthFeedbackState.success
2025-07-30 10:51:22.315074: 返回成功组件 (详细信息)
2025-07-30 10:51:22.315275: === 构建成功组件（详细信息） ===
2025-07-30 10:51:22.315275: 用户: gd, ID: 111111
2025-07-30 10:51:22.374617: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.452484: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.532305: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.613675: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.692177: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.773349: AuthView: 检测到 1 个人脸
2025-07-30 10:51:22.853914: AuthView: 检测到 1 个人脸
2025-07-30 10:51:23.895981: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 10:51:23.895981: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:23.896828: 认证完成，恢复人脸轮询
2025-07-30 10:51:25.302499: 定时器触发，切换到通行模式
2025-07-30 10:51:25.302499: 认证反馈状态切换到通行模式，启动5秒隐藏定时器
2025-07-30 10:51:25.315114: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:25.315114: 当前状态: AuthFeedbackState.passMode
2025-07-30 10:51:25.315114: 返回通行模式组件
2025-07-30 10:51:25.316111: === 构建通行模式组件 ===
2025-07-30 10:51:25.316111: 用户: gd, ID: 111111
2025-07-30 10:51:25.316111: 认证方式: AuthMethod.face
2025-07-30 10:51:25.898789: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:25.898)
2025-07-30 10:51:25.898789: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:25.898)
2025-07-30 10:51:25.899786: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:25.899786: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:25.899786: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:25.899786: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 140.531494140625, 数据大小: 0 bytes
2025-07-30 10:51:25.899786: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:25.899786: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:25.899786: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 4373 bytes, 实际置信度: 140.531494140625
2025-07-30 10:51:25.899786: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:25.899786: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:25.899786: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:25.899786: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:25.899786: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:26.019020: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:26.020016: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "db9ed1f6ed191e7ded873f41f00b9a1c",
		"log_id" : "1753843886019",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 86.0,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:26.020016: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:26.020016: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=86.0
2025-07-30 10:51:26.020016: 人脸识别成功，得分: 86.0，用户ID: 111111
2025-07-30 10:51:26.020016: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 10:51:26.020016: 🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...
2025-07-30 10:51:26.020016: 🔍 开始请求读者信息: 用户ID=111111
2025-07-30 10:51:26.021014: 63 CardType 值为空
2025-07-30 10:51:26.021014: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250730    105126  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY4AZF021
2025-07-30 10:51:26.219418: Rsp : 64              00120250730    105252000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY4AZDBC1
2025-07-30 10:51:26.235825: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105252, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 4AZDBC1}
2025-07-30 10:51:26.236835: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105252, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 4AZDBC1}
2025-07-30 10:51:26.236835: ✅ 读者信息获取成功: 姓名=gd, ID=111111
2025-07-30 10:51:26.236835: 🚀 FaceAuthService: 发送包含真实姓名的认证结果
2025-07-30 10:51:26.236835: 收到认证结果但当前状态不是监听状态: completed
2025-07-30 10:51:28.021235: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 10:51:28.021235: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:28.021235: 认证完成，恢复人脸轮询
2025-07-30 10:51:30.022731: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:30.022)
2025-07-30 10:51:30.022731: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 10:51:30.022)
2025-07-30 10:51:30.022731: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 10:51:30.304101: 认证完成，还原到默认显示方式
2025-07-30 10:51:30.314074: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:30.314074: === 构建多认证底部组件 ===
2025-07-30 10:51:30.314074: 显示底部组件: false
2025-07-30 10:51:30.315072: 反馈状态: AuthFeedbackState.idle
2025-07-30 10:51:30.315072: 用户信息: null (null)
2025-07-30 10:51:30.315072: 底部组件被隐藏，返回空组件
2025-07-30 10:51:31.643941: ready error :HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:51:31.644444: ready done
2025-07-30 10:51:31.644444: open reader readerType ：2 ret：-1
2025-07-30 10:51:31.644444: Error: WebSocketChannelException: WebSocketChannelException: HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:51:31.645444: open reader readerType ：13 ret：0
2025-07-30 10:51:31.645444: [[10, 0], [2, -1], [13, 0]]
2025-07-30 10:51:31.645444: changeType:ReaderErrorType.openSuccess
2025-07-30 10:51:31.645444: 读卡器状态变化: ReaderErrorType.openSuccess
2025-07-30 10:51:31.646442: WebSocket 连接已关闭
2025-07-30 10:51:32.023182: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:32.023)
2025-07-30 10:51:32.023182: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 10:51:32.023)
2025-07-30 10:51:32.023182: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 10:51:32.295108: 多认证管理器: 认证结果显示完成，重启所有认证监听并还原显示
2025-07-30 10:51:32.295108: 多认证管理器: 重新启动认证监听
2025-07-30 10:51:32.295108: 认证监听未在运行中
2025-07-30 10:51:32.496770: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:32.496770: 多认证管理器状态变更: listening
2025-07-30 10:51:32.497747: 启动所有认证方式监听: [AuthMethod.face, AuthMethod.readerCard, AuthMethod.socialSecurityCard]
2025-07-30 10:51:32.497747: 准备启动 2 个物理认证服务
2025-07-30 10:51:32.497747: 服务已在监听中，跳过启动
2025-07-30 10:51:32.497747: 开始读卡器认证监听
2025-07-30 10:51:32.497747: 强制重新配置读卡器以确保状态一致性
2025-07-30 10:51:32.497747: 完全重置读卡器连接和监听器状态...
2025-07-30 10:51:32.497747: 已移除读卡器状态监听器
2025-07-30 10:51:32.497747: 已移除标签数据监听器
2025-07-30 10:51:32.497747: 所有卡片监听器已移除
2025-07-30 10:51:32.497747: stopInventory newPort:SendPort
2025-07-30 10:51:32.498746: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:32.498746: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:32.514702: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:32.514702: === 构建多认证底部组件 ===
2025-07-30 10:51:32.514702: 显示底部组件: false
2025-07-30 10:51:32.514702: 反馈状态: AuthFeedbackState.idle
2025-07-30 10:51:32.514702: 用户信息: null (null)
2025-07-30 10:51:32.514702: 底部组件被隐藏，返回空组件
2025-07-30 10:51:32.699918: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:32.699918: subThread :ReaderCommand.close
2025-07-30 10:51:32.699918: commandRsp:ReaderCommand.close
2025-07-30 10:51:32.699918: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: 172.16.1.103:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}})
2025-07-30 10:51:32.699918: close:T10Bridge
2025-07-30 10:51:32.700916: dc_exit:0
2025-07-30 10:51:32.700916: close:HWFaceBridge
2025-07-30 10:51:32.700916: isConnect:false
2025-07-30 10:51:32.700916: disconnect
2025-07-30 10:51:32.700916: close:HD100SSBridge
2025-07-30 10:51:32.700916: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-07-30 10:51:32.700916: HD100SS close result: -1
2025-07-30 10:51:32.700916: close:done
2025-07-30 10:51:32.701914: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:32.701914: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:32.701914: already close all reader
2025-07-30 10:51:32.802530: 读卡器连接已完全关闭
2025-07-30 10:51:32.802530: 读卡器连接和监听器状态已完全重置
2025-07-30 10:51:32.802530: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 10:51:32.802530: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 10:51:32.802530: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 10:51:32.803506: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-07-30 10:51:32.803506: 添加有效设备: type=10, id=10
2025-07-30 10:51:32.803506: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 10:51:32.803506: 添加有效设备: type=2, id=2
2025-07-30 10:51:32.803506: 验证读卡器配置: 类型=13, 解码器=不解析
2025-07-30 10:51:32.803506: 添加有效设备: type=13, id=13
2025-07-30 10:51:32.803506: 总共加载了3个设备配置
2025-07-30 10:51:32.803506: stopInventory newPort:SendPort
2025-07-30 10:51:32.803506: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:32.804509: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:32.905780: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:32.905780: 读卡器连接已完全关闭
2025-07-30 10:51:32.905780: changeReaders
2025-07-30 10:51:32.905780: createIsolate isOpen:true,isOpening:false
2025-07-30 10:51:32.905780: open():SendPort
2025-07-30 10:51:32.905780: untilDetcted():SendPort
2025-07-30 10:51:32.906777: 读卡器配置完成，共 3 个设备
2025-07-30 10:51:32.906777: 已移除读卡器状态监听器
2025-07-30 10:51:32.906777: 已移除标签数据监听器
2025-07-30 10:51:32.906777: 所有卡片监听器已移除
2025-07-30 10:51:32.906777: 已添加读卡器状态监听器
2025-07-30 10:51:32.906777: 已添加标签数据监听器
2025-07-30 10:51:32.906777: 开始监听卡片数据 - 所有监听器已就绪
2025-07-30 10:51:32.906777: 读卡器认证监听启动成功
2025-07-30 10:51:32.906777: 读者证、社保卡 认证服务启动成功
2025-07-30 10:51:32.907775: 所有认证服务启动完成，成功启动 1 个服务
2025-07-30 10:51:32.907775: 当前可用的认证方式: 人脸识别、读者证、社保卡
2025-07-30 10:51:32.907775: 多认证管理器: 认证监听已重新启动
2025-07-30 10:51:32.907775: subThread :ReaderCommand.close
2025-07-30 10:51:32.907775: commandRsp:ReaderCommand.close
2025-07-30 10:51:32.907775: cacheUsedReaders:()
2025-07-30 10:51:32.907775: close:done
2025-07-30 10:51:32.907775: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:32.907775: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:32.908772: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:32.908772: already close all reader
2025-07-30 10:51:32.908772: subThread :ReaderCommand.readerList
2025-07-30 10:51:32.908772: commandRsp:ReaderCommand.readerList
2025-07-30 10:51:32.908772: readerList：3,readerSetting：3
2025-07-30 10:51:32.908772: cacheUsedReaders:3
2025-07-30 10:51:32.908772: subThread :ReaderCommand.open
2025-07-30 10:51:32.909769: commandRsp:ReaderCommand.open
2025-07-30 10:51:33.005383: dc_init:0xb4 100
2025-07-30 10:51:33.006352: open reader readerType ：10 ret：0
2025-07-30 10:51:33.006352: wsurl:172.16.1.103:9091
2025-07-30 10:51:33.006352: subThread :ReaderCommand.untilDetected
2025-07-30 10:51:33.006352: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:51:33.442389: ready error :HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:51:33.442893: ready done
2025-07-30 10:51:33.442893: open reader readerType ：2 ret：-1
2025-07-30 10:51:33.442893: changeType:ReaderErrorType.openFail
2025-07-30 10:51:33.442893: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 10:51:33.443894: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 10:51:33.443894: 读卡器连接失败，尝试重新连接
2025-07-30 10:51:33.443894: 处理读卡器连接错误
2025-07-30 10:51:33.443894: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
2025-07-30 10:51:33.509071: dc_config_card:0
2025-07-30 10:51:33.597833: dc_card_n_hex:0,len:4
2025-07-30 10:51:33.597833: parseHD100Info_2:[65, 53, 55, 55, 69, 66, 50, 68]
2025-07-30 10:51:33.605813: dc_authentication_pass:0
2025-07-30 10:51:33.613791: dc_read:0
2025-07-30 10:51:33.613791: data:31313131313100000000000000000000,coder:Std14443ACoder
2025-07-30 10:51:33.613791: parseRet：{"barCode":"111111"},decoder:14443AStd
2025-07-30 10:51:33.614788: 检测到读卡器数据: 1条
2025-07-30 10:51:33.614788: 读卡器数据认证：
2025-07-30 10:51:33.614788:   设备类型: 10
2025-07-30 10:51:33.614788:   条码: 111111
2025-07-30 10:51:33.614788:   标签UID: A577EB2D
2025-07-30 10:51:33.614788:   对应登录类型: AuthLoginType.readerCard
2025-07-30 10:51:33.614788:   根据读卡器类型10确定认证方式为: 读者证
2025-07-30 10:51:33.614788:   开始调用认证API: 111111
2025-07-30 10:51:33.614788: 正在认证用户: 111111, 方式: 读者证
2025-07-30 10:51:33.615786: 63 CardType 值为空
2025-07-30 10:51:33.615786: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250730    105133  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY5AZF022
2025-07-30 10:51:33.615786: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:33.821586: Rsp : 64              00120250730    105259000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY5AZDBB9
2025-07-30 10:51:33.826574: 63 CardType 值为空
2025-07-30 10:51:33.826574: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250730    105133  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY6AZF021
2025-07-30 10:51:33.830594: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105259, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBB9}
2025-07-30 10:51:33.830594: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105259, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 5AZDBB9}
2025-07-30 10:51:33.831565: SIP2认证成功: 用户=gd, ID=111111
2025-07-30 10:51:33.831565:   认证结果: AuthStatus.success, 方式: 读者证
2025-07-30 10:51:33.831565: 认证成功，停止读卡器监听
2025-07-30 10:51:33.831565: 检测到标签数据: 1条
2025-07-30 10:51:33.831565: 标签数据认证：
2025-07-30 10:51:33.831565:   设备类型: 10
2025-07-30 10:51:33.832556:   条码: 111111
2025-07-30 10:51:33.832556:   标签UID: A577EB2D
2025-07-30 10:51:33.832556:   对应登录类型: AuthLoginType.readerCard
2025-07-30 10:51:33.832556:   根据标签读卡器类型10确定认证方式为: 读者证
2025-07-30 10:51:33.832556:   开始调用认证API: 111111
2025-07-30 10:51:33.832556: 正在认证用户: 111111, 方式: 读者证
2025-07-30 10:51:33.832556: 多认证管理器: 收到认证结果: 读者证 - success
2025-07-30 10:51:33.832556: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:33.832556: 多认证管理器状态变更: authenticating
2025-07-30 10:51:33.832556: 多认证管理器: 切换显示方式 人脸识别 -> 读者证
2025-07-30 10:51:33.832556: 多认证管理器状态变化，当前显示方式: 读者证
2025-07-30 10:51:33.833554: 多认证管理器: 认证成功，立即停止其他认证方式
2025-07-30 10:51:33.833554: 停止人脸认证监听
2025-07-30 10:51:33.833554: 🛑 停止人脸识别监听...
2025-07-30 10:51:33.833554: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:33.833554: ✅ 人脸识别监听已停止
2025-07-30 10:51:33.833554: 停止读卡器认证监听
2025-07-30 10:51:33.833554: 已发起停止其他认证方式的操作
2025-07-30 10:51:33.833554: 多认证管理器状态变化，当前显示方式: 读者证
2025-07-30 10:51:33.833554: 多认证管理器状态变更: completed
2025-07-30 10:51:33.834552: 收到认证结果但当前状态不是监听状态: completed
2025-07-30 10:51:33.834552: FaceRecognitionAuthService监听已停止
2025-07-30 10:51:33.834552: 已移除读卡器状态监听器
2025-07-30 10:51:33.834552: 已移除标签数据监听器
2025-07-30 10:51:33.834552: 所有卡片监听器已移除
2025-07-30 10:51:33.834552: 没有活跃的读卡器连接需要暂停
2025-07-30 10:51:33.834552: 收到多认证结果: 读者证 - success
2025-07-30 10:51:33.835548: 认证成功，更新主显示方式为: 读者证
2025-07-30 10:51:33.835548: === 更新认证反馈状态 ===
2025-07-30 10:51:33.835548: 结果状态: AuthStatus.success
2025-07-30 10:51:33.835548: 用户姓名: gd
2025-07-30 10:51:33.835548: 用户ID: 111111
2025-07-30 10:51:33.835548: 设置反馈状态为success，显示底部组件: true
2025-07-30 10:51:33.835548: 反馈状态: AuthFeedbackState.success
2025-07-30 10:51:33.835548: 用户信息: gd (111111)
2025-07-30 10:51:33.835548: 认证结果流订阅已取消
2025-07-30 10:51:33.836545: 读卡器认证监听已停止（连接保持）
2025-07-30 10:51:33.836545: 已停止 socialSecurityCard 认证监听
2025-07-30 10:51:33.836545: 人脸认证监听已停止
2025-07-30 10:51:33.836545: 已停止 face 认证监听
2025-07-30 10:51:33.850530: 构建认证区域，认证方式: AuthMethod.readerCard
2025-07-30 10:51:33.850530: 构建读者证认证界面
2025-07-30 10:51:33.850530: === 构建多认证底部组件 ===
2025-07-30 10:51:33.850530: 显示底部组件: true
2025-07-30 10:51:33.850530: 反馈状态: AuthFeedbackState.success
2025-07-30 10:51:33.850530: 用户信息: gd (111111)
2025-07-30 10:51:33.851506: 创建AuthFeedbackWidget
2025-07-30 10:51:33.851506: 实际认证方式: AuthMethod.readerCard
2025-07-30 10:51:33.851506: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:33.851506: 当前状态: AuthFeedbackState.success
2025-07-30 10:51:33.851506: 返回成功组件 (详细信息)
2025-07-30 10:51:33.851506: === 构建成功组件（详细信息） ===
2025-07-30 10:51:33.851506: 用户: gd, ID: 111111
2025-07-30 10:51:33.851506: CameraPreviewWidget: 开始清理资源...
2025-07-30 10:51:33.851506: CameraPreviewWidget: 停止帧捕获定时器
2025-07-30 10:51:33.851506: CameraPreviewWidget: 停止人脸识别服务...
2025-07-30 10:51:33.851506: CameraPreviewWidget: 资源清理完成
2025-07-30 10:51:33.852504: === AuthFeedbackWidget 状态变化 ===
2025-07-30 10:51:33.852504: 新状态: AuthFeedbackState.success
2025-07-30 10:51:33.852504: 用户信息: gd (111111)
2025-07-30 10:51:33.852504: 启动定时器，3秒后切换到通行模式
2025-07-30 10:51:33.852504: CameraPreviewWidget: 人脸识别服务已清理
2025-07-30 10:51:33.864502: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:33.864502: 当前状态: AuthFeedbackState.success
2025-07-30 10:51:33.864502: 返回成功组件 (详细信息)
2025-07-30 10:51:33.864502: === 构建成功组件（详细信息） ===
2025-07-30 10:51:33.865470: 用户: gd, ID: 111111
2025-07-30 10:51:34.074807: Rsp : 64              00120250730    105300000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY6AZDBC5
2025-07-30 10:51:34.081768: 63 CardType 值为空
2025-07-30 10:51:34.081768: Req msgType：Sip2MsgType.patronInformation ,length:71， ret:  6300120250730    105134  Y       AOhlsp|AA111111|TY|BP1|BQ20|AY7AZF01F
2025-07-30 10:51:34.086794: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105300, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBC5}
2025-07-30 10:51:34.086794: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105300, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 6AZDBC5}
2025-07-30 10:51:34.086794: stopInventory newPort:SendPort
2025-07-30 10:51:34.113683: 保存登录记录成功: 111111
2025-07-30 10:51:34.113683: 已通知认证成功状态变更
2025-07-30 10:51:34.139656: 保存认证记录到access_logs表成功
2025-07-30 10:51:34.139656: 认证成功: 读者姓名=gd, 读者编号=111111
2025-07-30 10:51:34.139656: 调用认证后处理服务: gd, 认证方式: 人脸认证
2025-07-30 10:51:34.140654: 执行认证后处理流程，共2个处理器
2025-07-30 10:51:34.140654: 执行处理器: DoorLockHandler
2025-07-30 10:51:34.140654: 开始执行自动开门处理 - 用户: gd, 认证方式: 人脸认证
2025-07-30 10:51:34.140654: 找到启用的门锁配置: 主门锁设备
2025-07-30 10:51:34.140654: 使用门锁配置: 主门锁设备 (COM1)
2025-07-30 10:51:34.140654: 使用认证页面的门锁连接: COM1
2025-07-30 10:51:34.140654: 使用继电器通道: 1
2025-07-30 10:51:34.140654: 使用继电器通道: 1
2025-07-30 10:51:34.141650: 正在打开绿灯...
2025-07-30 10:51:34.141650: 准备发送命令 [GLED打开], 命令键: 31_37
2025-07-30 10:51:34.141650: 发送命令 [GLED打开]: A0 A3 00 02 31 37 A7 0A
2025-07-30 10:51:34.141650: 等待响应 [GLED打开], 超时时间: 3000ms
2025-07-30 10:51:34.259364: iReadCardBas ret:4294967294
2025-07-30 10:51:34.259364: 无卡
2025-07-30 10:51:34.260333: 无卡
2025-07-30 10:51:34.260333: 检测到了卡 来暂停 ：1
2025-07-30 10:51:34.260333: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:34.260333: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:34.297255: Rsp : 64              00120250730    105300000100000000    0000    AOhlsp|AA111111|XO|AEgd|BZ0002|CA0000|BLY|ST001|CQY|BV0.0|AS10006811|JF0.0|BE|AF|AG|AY7AZDBC4
2025-07-30 10:51:34.311516: rspInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105300, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBC4}
2025-07-30 10:51:34.311516: patronInfo:{PatronStatus:               , Language: 001, TransactionDate: 20250730    105300, HoldItemsCount: 0001, OverdueItemCount: 0000, ChargedItemCount: 0000, FineItemCount:     , RecallItemsCount: 0000, UnavailableItemsCount:     , InstitutionId: hlsp, PersonalIdentifier: , BirthDate: , MobilePhone: , PatronValidDate: , PatronIdentifier: 111111, PersonName: gd, HoldItemsLimit: 0002, OverdueItemsLimit: 0000, ChargedItemsLimit: , Fee: , FeeLimit: , CurrencyType: , ValidPatron: Y, ValidPatronPassword: Y, FeeAmount: 0.0, ChargeAmount: , PatronTotalFine: 0.0, Depositrate: , LoanedValue: , ReaderType: , EndDate: , HoldItems: 10006811, OverdueItems: , ChargedItems: , FineItems: , RecallItems: , UnavailableItems: , HomeAddress: , EmailAddress: , HomePhoneNumber: , LL: , GL: , Score: , ScreenMessage: , PrintLine: , MsgSeqId: 7AZDBC4}
2025-07-30 10:51:34.311516: SIP2认证成功: 用户=gd, ID=111111
2025-07-30 10:51:34.311516:   认证结果: AuthStatus.success, 方式: 读者证
2025-07-30 10:51:34.311516: 认证成功，停止标签监听
2025-07-30 10:51:34.312481: 收到认证结果但当前状态不是监听状态: completed
2025-07-30 10:51:34.312481: 收到认证结果但当前状态不是监听状态: completed
2025-07-30 10:51:36.852154: 定时器触发，切换到通行模式
2025-07-30 10:51:36.852154: 认证反馈状态切换到通行模式，启动5秒隐藏定时器
2025-07-30 10:51:36.864251: === AuthFeedbackWidget 构建内容 ===
2025-07-30 10:51:36.865252: 当前状态: AuthFeedbackState.passMode
2025-07-30 10:51:36.865252: 返回通行模式组件
2025-07-30 10:51:36.865252: === 构建通行模式组件 ===
2025-07-30 10:51:36.865252: 用户: gd, ID: 111111
2025-07-30 10:51:36.865252: 认证方式: AuthMethod.readerCard
2025-07-30 10:51:37.143450: 命令响应超时 [GLED打开], 命令键: 31_37, 当前等待命令数: 0
2025-07-30 10:51:37.143450: 发送命令失败 [GLED打开]: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 10:51:37.143450: 控制LED异常: DoorRelayException: 命令响应超时: GLED打开
2025-07-30 10:51:37.143450: 绿灯打开失败: 操作失败
2025-07-30 10:51:37.143450: 正在开门...
2025-07-30 10:51:37.144421: 准备发送命令 [继电器1打开], 命令键: 31_31
2025-07-30 10:51:37.144421: 发送命令 [继电器1打开]: A0 A3 00 02 31 31 A1 0A
2025-07-30 10:51:37.144421: 等待响应 [继电器1打开], 超时时间: 3000ms
2025-07-30 10:51:40.144891: 命令响应超时 [继电器1打开], 命令键: 31_31, 当前等待命令数: 0
2025-07-30 10:51:40.144891: 发送命令失败 [继电器1打开]: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 10:51:40.144891: 控制继电器异常: DoorRelayException: 命令响应超时: 继电器1打开
2025-07-30 10:51:40.144891: 开门失败: 操作失败
2025-07-30 10:51:40.144891: 准备发送命令 [GLED关闭], 命令键: 32_37
2025-07-30 10:51:40.145900: 发送命令 [GLED关闭]: A0 A3 00 02 32 37 A4 0A
2025-07-30 10:51:40.145900: 等待响应 [GLED关闭], 超时时间: 3000ms
2025-07-30 10:51:41.854081: 多认证管理器: 还原到默认显示方式: 人脸识别
2025-07-30 10:51:41.854081: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:41.854081: 认证完成，还原到默认显示方式
2025-07-30 10:51:43.517329: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:43.517329: === 构建多认证底部组件 ===
2025-07-30 10:51:43.518326: 显示底部组件: false
2025-07-30 10:51:43.518326: 反馈状态: AuthFeedbackState.idle
2025-07-30 10:51:43.518326: 用户信息: null (null)
2025-07-30 10:51:43.518326: 底部组件被隐藏，返回空组件
2025-07-30 10:51:43.518326: 📷 CameraPreviewWidget: 检查摄像头检测器初始化状态...
2025-07-30 10:51:43.518326: ✅ CameraPreviewWidget: 摄像头检测器已初始化，跳过初始化步骤
2025-07-30 10:51:43.518326: 📹 CameraPreviewWidget: 启动摄像头 0...
2025-07-30 10:51:43.518326: ✅ CameraPreviewWidget: 摄像头启动成功，耗时: 1653ms
2025-07-30 10:51:43.518326: CameraPreviewWidget: 预加载DNN模型...
2025-07-30 10:51:43.519323: CameraPreviewWidget: DNN模型预加载完成，获取到 0 个结果
2025-07-30 10:51:43.519323: CameraPreviewWidget: 帧捕获已启动，间隔: 60ms
2025-07-30 10:51:43.519323: CameraPreviewWidget: 人脸检测器已启动，检测间隔: 80ms
2025-07-30 10:51:43.519323: ✅ CameraPreviewWidget: 摄像头初始化完成
2025-07-30 10:51:43.520321: 命令响应超时 [GLED关闭], 命令键: 32_37, 当前等待命令数: 0
2025-07-30 10:51:43.520321: 发送命令失败 [GLED关闭]: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 10:51:43.520321: 控制LED异常: DoorRelayException: 命令响应超时: GLED关闭
2025-07-30 10:51:43.520321: 门锁操作完成，连接由认证页面管理
2025-07-30 10:51:43.520321: 自动开门失败 - 用户: gd
2025-07-30 10:51:43.520321: 执行处理器: WelcomeHandler
2025-07-30 10:51:43.520321: 显示欢迎信息给: gd
2025-07-30 10:51:43.520321: 欢迎用户: gd，认证方式: 人脸认证
2025-07-30 10:51:43.520321: 认证后处理流程执行完毕
2025-07-30 10:51:43.833484: 多认证管理器: 认证结果显示完成，重启所有认证监听并还原显示
2025-07-30 10:51:43.833484: 多认证管理器: 重新启动认证监听
2025-07-30 10:51:43.833484: 认证监听未在运行中
2025-07-30 10:51:46.032218: 多认证管理器状态变化，当前显示方式: 人脸识别
2025-07-30 10:51:46.032218: 多认证管理器状态变更: listening
2025-07-30 10:51:46.032218: 启动所有认证方式监听: [AuthMethod.face, AuthMethod.readerCard, AuthMethod.socialSecurityCard]
2025-07-30 10:51:46.033216: 准备启动 2 个物理认证服务
2025-07-30 10:51:46.033216: 开始人脸认证监听
2025-07-30 10:51:46.033216: 人脸识别服务未初始化，尝试自动重新初始化...
2025-07-30 10:51:46.033216: 📷 开始人脸识别认证服务初始化流程...
2025-07-30 10:51:46.033216: 🔧 第一步：初始化摄像头系统...
2025-07-30 10:51:46.033216: 📷 检查人脸检测器初始化状态...
2025-07-30 10:51:46.033216: ✅ 人脸检测器已初始化，跳过初始化步骤
2025-07-30 10:51:46.033216: 🔍 检测可用摄像头设备...
2025-07-30 10:51:46.033216: ✅ 检测到 1 个摄像头设备: [0]
2025-07-30 10:51:46.033216: 📹 启动摄像头（带重试机制）...
2025-07-30 10:51:46.033216: 📹 尝试使用摄像头ID: 0
2025-07-30 10:51:46.033216: 📹 摄像头启动尝试 1/2，ID: 0
2025-07-30 10:51:46.033216: ✅ 摄像头启动成功，ID: 0，尝试次数: 1，耗时: 1321ms，后端: DirectShow
2025-07-30 10:51:46.034213: 🔧 配置摄像头参数...
2025-07-30 10:51:46.034213: ✅ 摄像头参数配置完成
2025-07-30 10:51:46.034213: 🤖 预加载人脸检测模型...
2025-07-30 10:51:46.034213: 预加载DNN模型...
2025-07-30 10:51:46.034213: DNN模型预加载完成，获取到 0 个结果
2025-07-30 10:51:46.034213: ✅ 人脸检测模型预加载完成
2025-07-30 10:51:46.034213: 🔍 验证摄像头功能...
2025-07-30 10:51:46.034213: ✅ 摄像头功能验证成功，帧数据大小: 230400 bytes
2025-07-30 10:51:46.035210: ✅ 人脸检测器初始化完成
2025-07-30 10:51:46.035210: ✅ 摄像头系统初始化成功
2025-07-30 10:51:46.035210: 🔧 第二步：初始化百度人脸识别SDK...
2025-07-30 10:51:46.035210: 从配置获取SDK路径: D:\FaceOfflineSdk
2025-07-30 10:51:46.035210: ✅ SDK路径验证成功
2025-07-30 10:51:46.035210: 🔧 配置SDK路径: D:\FaceOfflineSdk
2025-07-30 10:51:46.036207: SDK路径设置成功: D:\FaceOfflineSdk
2025-07-30 10:51:46.036207: ✅ SDK路径配置完成，错误码: 0
2025-07-30 10:51:46.036207: 🚀 开始初始化百度人脸识别SDK...
2025-07-30 10:51:46.037205: ✅ 百度人脸识别SDK初始化成功
2025-07-30 10:51:46.037205: ✅ 百度SDK状态: 已初始化，人脸数量: 4，授权状态: 已授权
2025-07-30 10:51:46.037205: ✅ 百度人脸识别SDK初始化成功
2025-07-30 10:51:46.037205: 🔧 第三步：重新创建认证结果流控制器...
2025-07-30 10:51:46.037205: ✅ 认证结果流控制器重新创建成功
2025-07-30 10:51:46.037205: 🔧 第四步：启动系统监控...
2025-07-30 10:51:46.037205: ✅ 系统监控启动成功
2025-07-30 10:51:46.037205: ✅ 人脸识别认证服务初始化完成
2025-07-30 10:51:46.037205: 人脸识别服务自动重新初始化成功
2025-07-30 10:51:46.037205: 🚀 开始人脸识别监听（使用FaceCapturePolling方式）...
2025-07-30 10:51:46.038202: 🆕 创建新的人脸捕获轮询服务...
2025-07-30 10:51:46.038202: ✅ 人脸捕获轮询服务初始化成功
2025-07-30 10:51:46.038202: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:46.038202: ✅ 人脸识别监听启动成功
2025-07-30 10:51:46.038202: 人脸认证监听启动成功
2025-07-30 10:51:46.038202: 人脸识别 认证服务启动成功
2025-07-30 10:51:46.038202: 开始读卡器认证监听
2025-07-30 10:51:46.038202: 强制重新配置读卡器以确保状态一致性
2025-07-30 10:51:46.038202: 完全重置读卡器连接和监听器状态...
2025-07-30 10:51:46.038202: 已移除读卡器状态监听器
2025-07-30 10:51:46.038202: 已移除标签数据监听器
2025-07-30 10:51:46.038202: 所有卡片监听器已移除
2025-07-30 10:51:46.038202: stopInventory newPort:SendPort
2025-07-30 10:51:46.041194: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:46.041194: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:46.048176: 构建认证区域，认证方式: AuthMethod.face
2025-07-30 10:51:46.049174: === 构建多认证底部组件 ===
2025-07-30 10:51:46.049174: 显示底部组件: false
2025-07-30 10:51:46.049174: 反馈状态: AuthFeedbackState.idle
2025-07-30 10:51:46.049174: 用户信息: null (null)
2025-07-30 10:51:46.049174: 底部组件被隐藏，返回空组件
2025-07-30 10:51:46.240975: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:46.240975: 读卡器连接已完全关闭
2025-07-30 10:51:46.240975: 读卡器连接和监听器状态已完全重置
2025-07-30 10:51:46.240975: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 10:51:46.241972: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 10:51:46.241972: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 10:51:46.241972: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-07-30 10:51:46.241972: 添加有效设备: type=10, id=10
2025-07-30 10:51:46.241972: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 10:51:46.241972: 添加有效设备: type=2, id=2
2025-07-30 10:51:46.241972: 验证读卡器配置: 类型=13, 解码器=不解析
2025-07-30 10:51:46.241972: 添加有效设备: type=13, id=13
2025-07-30 10:51:46.241972: 总共加载了3个设备配置
2025-07-30 10:51:46.241972: stopInventory newPort:SendPort
2025-07-30 10:51:46.241972: subThread :ReaderCommand.close
2025-07-30 10:51:46.241972: commandRsp:ReaderCommand.close
2025-07-30 10:51:46.242969: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: 172.16.1.103:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}})
2025-07-30 10:51:46.242969: close:T10Bridge
2025-07-30 10:51:46.242969: dc_exit:0
2025-07-30 10:51:46.242969: close:HWFaceBridge
2025-07-30 10:51:46.242969: isConnect:false
2025-07-30 10:51:46.242969: disconnect
2025-07-30 10:51:46.242969: close:HD100SSBridge
2025-07-30 10:51:46.242969: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-07-30 10:51:46.243967: HD100SS close result: -1
2025-07-30 10:51:46.243967: close:done
2025-07-30 10:51:46.243967: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:46.243967: already close all reader
2025-07-30 10:51:46.243967: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:46.243967: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:46.343046: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:46.344043: 读卡器连接已完全关闭
2025-07-30 10:51:46.344043: changeReaders
2025-07-30 10:51:46.344043: createIsolate isOpen:true,isOpening:false
2025-07-30 10:51:46.344043: open():SendPort
2025-07-30 10:51:46.344043: untilDetcted():SendPort
2025-07-30 10:51:46.344043: 读卡器配置完成，共 3 个设备
2025-07-30 10:51:46.345040: 已移除读卡器状态监听器
2025-07-30 10:51:46.345040: 已移除标签数据监听器
2025-07-30 10:51:46.345040: 所有卡片监听器已移除
2025-07-30 10:51:46.345040: 已添加读卡器状态监听器
2025-07-30 10:51:46.345040: 已添加标签数据监听器
2025-07-30 10:51:46.345040: 开始监听卡片数据 - 所有监听器已就绪
2025-07-30 10:51:46.345040: 读卡器认证监听启动成功
2025-07-30 10:51:46.345040: 读者证、社保卡 认证服务启动成功
2025-07-30 10:51:46.346039: 所有认证服务启动完成，成功启动 2 个服务
2025-07-30 10:51:46.346039: 当前可用的认证方式: 人脸识别、读者证、社保卡
2025-07-30 10:51:46.346039: 多认证管理器: 认证监听已重新启动
2025-07-30 10:51:46.346039: subThread :ReaderCommand.close
2025-07-30 10:51:46.346039: commandRsp:ReaderCommand.close
2025-07-30 10:51:46.346039: cacheUsedReaders:()
2025-07-30 10:51:46.346039: close:done
2025-07-30 10:51:46.346039: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:46.347036: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:46.347036: already close all reader
2025-07-30 10:51:46.347036: subThread :ReaderCommand.readerList
2025-07-30 10:51:46.347036: commandRsp:ReaderCommand.readerList
2025-07-30 10:51:46.347036: readerList：3,readerSetting：3
2025-07-30 10:51:46.347036: cacheUsedReaders:3
2025-07-30 10:51:46.347036: subThread :ReaderCommand.open
2025-07-30 10:51:46.348033: commandRsp:ReaderCommand.open
2025-07-30 10:51:46.445801: dc_init:0xb4 100
2025-07-30 10:51:46.445801: open reader readerType ：10 ret：0
2025-07-30 10:51:46.445801: wsurl:172.16.1.103:9091
2025-07-30 10:51:46.445801: subThread :ReaderCommand.untilDetected
2025-07-30 10:51:46.446657: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:51:46.949857: dc_config_card:0
2025-07-30 10:51:46.965815: dc_card_n_hex:1,len:0
2025-07-30 10:51:46.965815: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:47.612236: iReadCardBas ret:4294967294
2025-07-30 10:51:47.612236: 无卡
2025-07-30 10:51:47.612236: 无卡
2025-07-30 10:51:47.614235: dc_config_card:0
2025-07-30 10:51:47.629618: dc_card_n_hex:1,len:0
2025-07-30 10:51:47.629618: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:48.037369: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:48.037)
2025-07-30 10:51:48.037369: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 无新数据 (时间: 10:51:48.037)
2025-07-30 10:51:48.037369: 📝 [FaceCapturePolling] 无新数据，调用onNoFaceDetected回调
2025-07-30 10:51:48.276377: iReadCardBas ret:4294967294
2025-07-30 10:51:48.276377: 无卡
2025-07-30 10:51:48.276377: 无卡
2025-07-30 10:51:48.277374: dc_config_card:0
2025-07-30 10:51:48.293880: dc_card_n_hex:1,len:0
2025-07-30 10:51:48.293880: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:48.720655: AuthView: 检测到 1 个人脸
2025-07-30 10:51:48.802027: AuthView: 检测到 1 个人脸
2025-07-30 10:51:48.939096: iReadCardBas ret:4294967294
2025-07-30 10:51:48.940094: 无卡
2025-07-30 10:51:48.940094: 无卡
2025-07-30 10:51:48.940094: ready error :HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:51:48.940094: ready done
2025-07-30 10:51:48.940094: open reader readerType ：2 ret：-1
2025-07-30 10:51:48.940094: changeType:ReaderErrorType.openFail
2025-07-30 10:51:48.940094: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 10:51:48.940094: 读卡器连接失败，尝试重新连接
2025-07-30 10:51:48.941091: 处理读卡器连接错误
2025-07-30 10:51:48.941091: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
2025-07-30 10:51:48.941091: dc_config_card:0
2025-07-30 10:51:48.957079: dc_card_n_hex:1,len:0
2025-07-30 10:51:48.958045: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:49.201319: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.282072: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.360660: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.440753: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.522253: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.601498: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.603493: iReadCardBas ret:4294967294
2025-07-30 10:51:49.603493: 无卡
2025-07-30 10:51:49.604467: 无卡
2025-07-30 10:51:49.605487: dc_config_card:0
2025-07-30 10:51:49.621444: dc_card_n_hex:1,len:0
2025-07-30 10:51:49.621444: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:49.681290: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.761822: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.840583: AuthView: 检测到 1 个人脸
2025-07-30 10:51:49.921339: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.002228: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.037441: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:50.037)
2025-07-30 10:51:50.038442: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:50.037)
2025-07-30 10:51:50.038442: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:50.038442: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:50.038442: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:50.039459: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 146.546630859375, 数据大小: 0 bytes
2025-07-30 10:51:50.039459: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:50.039459: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:50.039459: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3693 bytes, 实际置信度: 146.546630859375
2025-07-30 10:51:50.039459: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:50.039459: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:50.039459: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:50.039459: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:50.039459: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:50.156690: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:50.156690: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "8f88fe5321022e8a38f76e1c9a7fe435",
		"log_id" : "1753843910157",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 90.88,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:50.156690: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:50.156690: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=90.88
2025-07-30 10:51:50.157719: 人脸识别成功，得分: 90.88，用户ID: 111111
2025-07-30 10:51:50.157719: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 10:51:50.159713: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.161707: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.241463: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.267399: iReadCardBas ret:4294967294
2025-07-30 10:51:50.268391: 无卡
2025-07-30 10:51:50.268391: 无卡
2025-07-30 10:51:50.269389: dc_config_card:0
2025-07-30 10:51:50.285435: dc_card_n_hex:1,len:0
2025-07-30 10:51:50.285435: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:50.320267: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.401228: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.482512: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.560817: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.640602: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.721048: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.801419: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.881237: AuthView: 检测到 1 个人脸
2025-07-30 10:51:50.931074: iReadCardBas ret:4294967294
2025-07-30 10:51:50.932070: 无卡
2025-07-30 10:51:50.932070: 无卡
2025-07-30 10:51:50.934068: dc_config_card:0
2025-07-30 10:51:50.943040: 尝试重新配置读卡器
2025-07-30 10:51:50.943040: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 10:51:50.943040: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 10:51:50.943040: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 10:51:50.943040: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-07-30 10:51:50.944038: 添加有效设备: type=10, id=10
2025-07-30 10:51:50.944038: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 10:51:50.944038: 添加有效设备: type=2, id=2
2025-07-30 10:51:50.944038: 验证读卡器配置: 类型=13, 解码器=不解析
2025-07-30 10:51:50.944038: 添加有效设备: type=13, id=13
2025-07-30 10:51:50.944038: 总共加载了3个设备配置
2025-07-30 10:51:50.944038: stopInventory newPort:SendPort
2025-07-30 10:51:50.949055: dc_card_n_hex:1,len:0
2025-07-30 10:51:50.950021: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:50.961989: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.041256: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.045259: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:51:51.045259: 读卡器连接已完全关闭
2025-07-30 10:51:51.045259: changeReaders
2025-07-30 10:51:51.045259: createIsolate isOpen:true,isOpening:false
2025-07-30 10:51:51.046223: open():SendPort
2025-07-30 10:51:51.046223: untilDetcted():SendPort
2025-07-30 10:51:51.046223: 读卡器配置完成，共 3 个设备
2025-07-30 10:51:51.121050: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.201264: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.281049: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.361364: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.442128: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.521364: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.595166: iReadCardBas ret:4294967294
2025-07-30 10:51:51.596162: 无卡
2025-07-30 10:51:51.596162: 无卡
2025-07-30 10:51:51.596162: subThread :ReaderCommand.stopInventory
2025-07-30 10:51:51.596162: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:51:51.596162: subThread :ReaderCommand.close
2025-07-30 10:51:51.596162: commandRsp:ReaderCommand.close
2025-07-30 10:51:51.596162: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: 172.16.1.103:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}})
2025-07-30 10:51:51.596162: close:T10Bridge
2025-07-30 10:51:51.597159: dc_exit:0
2025-07-30 10:51:51.597159: close:HWFaceBridge
2025-07-30 10:51:51.597159: isConnect:false
2025-07-30 10:51:51.597159: disconnect
2025-07-30 10:51:51.597159: close:HD100SSBridge
2025-07-30 10:51:51.597159: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-07-30 10:51:51.597159: HD100SS close result: -1
2025-07-30 10:51:51.597159: close:done
2025-07-30 10:51:51.597159: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:51:51.598157: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:51:51.598157: already close all reader
2025-07-30 10:51:51.598157: subThread :ReaderCommand.readerList
2025-07-30 10:51:51.598157: commandRsp:ReaderCommand.readerList
2025-07-30 10:51:51.598157: readerList：3,readerSetting：3
2025-07-30 10:51:51.598157: cacheUsedReaders:3
2025-07-30 10:51:51.598157: subThread :ReaderCommand.open
2025-07-30 10:51:51.598157: commandRsp:ReaderCommand.open
2025-07-30 10:51:51.601150: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.605139: dc_init:0xb4 100
2025-07-30 10:51:51.606135: open reader readerType ：10 ret：0
2025-07-30 10:51:51.606135: wsurl:172.16.1.103:9091
2025-07-30 10:51:51.606135: subThread :ReaderCommand.untilDetected
2025-07-30 10:51:51.606135: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:51:51.684925: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.760847: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.840462: AuthView: 检测到 1 个人脸
2025-07-30 10:51:51.921686: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.000826: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.080618: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.109463: dc_config_card:0
2025-07-30 10:51:52.125400: dc_card_n_hex:1,len:0
2025-07-30 10:51:52.125400: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:52.159309: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 10:51:52.159309: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:52.159309: 认证完成，恢复人脸轮询
2025-07-30 10:51:52.162321: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.241118: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.320195: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.401975: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.481238: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.561116: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.643861: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.720800: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.771436: iReadCardBas ret:4294967294
2025-07-30 10:51:52.771436: 无卡
2025-07-30 10:51:52.771436: 无卡
2025-07-30 10:51:52.773965: dc_config_card:0
2025-07-30 10:51:52.788926: dc_card_n_hex:1,len:0
2025-07-30 10:51:52.789923: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:52.800894: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.883685: AuthView: 检测到 1 个人脸
2025-07-30 10:51:52.961499: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.041250: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.121471: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.201320: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.281868: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.361741: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.435523: iReadCardBas ret:4294967294
2025-07-30 10:51:53.435523: 无卡
2025-07-30 10:51:53.435523: 无卡
2025-07-30 10:51:53.437545: dc_config_card:0
2025-07-30 10:51:53.441092: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.454012: dc_card_n_hex:1,len:0
2025-07-30 10:51:53.454012: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:53.520862: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.602611: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.681431: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.760656: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.842405: AuthView: 检测到 1 个人脸
2025-07-30 10:51:53.921194: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.001011: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.082206: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.099399: iReadCardBas ret:4294967294
2025-07-30 10:51:54.099399: 无卡
2025-07-30 10:51:54.100397: 无卡
2025-07-30 10:51:54.101421: dc_config_card:0
2025-07-30 10:51:54.117352: dc_card_n_hex:1,len:0
2025-07-30 10:51:54.117352: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:54.160473: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:54.160)
2025-07-30 10:51:54.160473: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:54.160)
2025-07-30 10:51:54.160473: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:54.160473: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:54.161469: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:54.161469: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 129.33294677734375, 数据大小: 0 bytes
2025-07-30 10:51:54.161469: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:54.161469: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:54.161469: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3056 bytes, 实际置信度: 129.33294677734375
2025-07-30 10:51:54.161469: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:54.161469: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:54.161469: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:54.161469: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:54.161469: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:54.278123: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:54.278123: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.278123: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "53654c7ebda7533af9ec519f370cc8e6",
		"log_id" : "1753843914278",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 68.92,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:54.278123: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:54.278123: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=68.92
2025-07-30 10:51:54.278123: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:54.278123: 认证完成，恢复人脸轮询
2025-07-30 10:51:54.281124: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.322450: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.401238: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.480767: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.562568: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.641207: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.721257: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.763745: iReadCardBas ret:4294967294
2025-07-30 10:51:54.764713: 无卡
2025-07-30 10:51:54.764713: 无卡
2025-07-30 10:51:54.765737: dc_config_card:0
2025-07-30 10:51:54.781684: dc_card_n_hex:1,len:0
2025-07-30 10:51:54.781684: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:54.801644: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.881418: AuthView: 检测到 1 个人脸
2025-07-30 10:51:54.961216: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.041402: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.120432: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.201174: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.282538: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.361095: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.427924: iReadCardBas ret:4294967294
2025-07-30 10:51:55.427924: 无卡
2025-07-30 10:51:55.427924: 无卡
2025-07-30 10:51:55.429910: dc_config_card:0
2025-07-30 10:51:55.441878: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.445870: dc_card_n_hex:1,len:0
2025-07-30 10:51:55.445870: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:55.522146: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.600935: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.681744: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.761312: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.841080: AuthView: 检测到 1 个人脸
2025-07-30 10:51:55.920886: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.001680: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.081435: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.092221: iReadCardBas ret:4294967294
2025-07-30 10:51:56.092221: 无卡
2025-07-30 10:51:56.092221: 无卡
2025-07-30 10:51:56.093900: dc_config_card:0
2025-07-30 10:51:56.109918: dc_card_n_hex:1,len:0
2025-07-30 10:51:56.109918: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:56.160407: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.242162: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.279774: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:56.279)
2025-07-30 10:51:56.280788: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:56.279)
2025-07-30 10:51:56.280788: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:56.280788: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:56.280788: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:56.280788: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 117.34276580810547, 数据大小: 0 bytes
2025-07-30 10:51:56.280788: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:56.280788: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:56.280788: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 2887 bytes, 实际置信度: 117.34276580810547
2025-07-30 10:51:56.280788: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:56.280788: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:56.281785: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:56.281785: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:56.281785: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:56.401540: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:56.401540: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "ab7250dfd6ec6f194aaf7b57fc9fef08",
		"log_id" : "1753843916401",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 55.99,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:56.401540: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:56.401540: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=55.99
2025-07-30 10:51:56.401540: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:51:56.401540: 认证完成，恢复人脸轮询
2025-07-30 10:51:56.403534: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.403534: 发送心跳
2025-07-30 10:51:56.403534: Req msgType：Sip2MsgType.scStatus ,length:20， ret:  9900522.00AY8AZFC99
2025-07-30 10:51:56.481327: Rsp : 98YYYNNN00500320250730    1053222.00AOhlsp|AM海恒图书馆|BXYYYYYYYYYYYYYYYY|AN3a40852d-49fd-4df2-a1f9-6e2641a6e91f|AF|AG|AY8AZD524
2025-07-30 10:51:56.561508: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.640547: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.721318: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.757120: iReadCardBas ret:4294967294
2025-07-30 10:51:56.757120: 无卡
2025-07-30 10:51:56.757120: 无卡
2025-07-30 10:51:56.765978: dc_config_card:0
2025-07-30 10:51:56.781498: dc_card_n_hex:1,len:0
2025-07-30 10:51:56.781498: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:56.801194: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.880491: AuthView: 检测到 1 个人脸
2025-07-30 10:51:56.961561: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.041679: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.120163: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.202342: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.281265: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.360105: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.427988: iReadCardBas ret:4294967294
2025-07-30 10:51:57.427988: 无卡
2025-07-30 10:51:57.428994: 无卡
2025-07-30 10:51:57.438071: dc_config_card:0
2025-07-30 10:51:57.441298: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.453924: dc_card_n_hex:1,len:0
2025-07-30 10:51:57.453924: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:57.521066: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.600339: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.681875: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.760921: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.840873: AuthView: 检测到 1 个人脸
2025-07-30 10:51:57.921433: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.001987: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.080156: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.100107: iReadCardBas ret:4294967294
2025-07-30 10:51:58.100107: 无卡
2025-07-30 10:51:58.100107: 无卡
2025-07-30 10:51:58.102100: dc_config_card:0
2025-07-30 10:51:58.117177: dc_card_n_hex:1,len:0
2025-07-30 10:51:58.118173: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:58.161390: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.240864: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.320691: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.402147: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.404115: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:51:58.403)
2025-07-30 10:51:58.404115: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:51:58.403)
2025-07-30 10:51:58.404115: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:51:58.404115: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:51:58.404115: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:51:58.405112: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 120.76375579833984, 数据大小: 0 bytes
2025-07-30 10:51:58.405112: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:51:58.405112: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:51:58.405112: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3448 bytes, 实际置信度: 120.76375579833984
2025-07-30 10:51:58.405112: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:51:58.405112: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:51:58.405112: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:51:58.405112: ⏹️ 停止人脸捕获轮询
2025-07-30 10:51:58.405112: 暂停人脸轮询，开始认证流程
2025-07-30 10:51:58.513070: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:51:58.514070: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "df5154e2fdd4df95095a849e216901db",
		"log_id" : "1753843918513",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 86.73,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:51:58.514070: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:51:58.514070: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=86.73
2025-07-30 10:51:58.514070: 人脸识别成功，得分: 86.73，用户ID: 111111
2025-07-30 10:51:58.514070: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 10:51:58.516062: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.560508: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.641837: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.720516: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.763776: iReadCardBas ret:4294967294
2025-07-30 10:51:58.764770: 无卡
2025-07-30 10:51:58.764770: 无卡
2025-07-30 10:51:58.765768: dc_config_card:0
2025-07-30 10:51:58.781840: dc_card_n_hex:1,len:0
2025-07-30 10:51:58.781840: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:58.801771: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.881161: AuthView: 检测到 1 个人脸
2025-07-30 10:51:58.961282: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.041723: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.121957: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.201927: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.361798: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.428745: iReadCardBas ret:4294967294
2025-07-30 10:51:59.428745: 无卡
2025-07-30 10:51:59.428745: 无卡
2025-07-30 10:51:59.437786: dc_config_card:0
2025-07-30 10:51:59.453902: dc_card_n_hex:1,len:0
2025-07-30 10:51:59.453902: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:51:59.520926: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.603374: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.681145: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.760824: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.841181: AuthView: 检测到 1 个人脸
2025-07-30 10:51:59.921107: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.000153: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.082932: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.099885: iReadCardBas ret:4294967294
2025-07-30 10:52:00.099885: 无卡
2025-07-30 10:52:00.100882: 无卡
2025-07-30 10:52:00.100882: dc_config_card:0
2025-07-30 10:52:00.117838: dc_card_n_hex:1,len:0
2025-07-30 10:52:00.117838: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:00.160723: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.241506: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.400736: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.482: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.515527: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 10:52:00.515527: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:52:00.515527: 认证完成，恢复人脸轮询
2025-07-30 10:52:00.640862: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.721443: AuthView: 检测到 1 个人脸
2025-07-30 10:52:00.763987: iReadCardBas ret:4294967294
2025-07-30 10:52:00.763987: 无卡
2025-07-30 10:52:00.764998: 无卡
2025-07-30 10:52:00.765986: dc_config_card:0
2025-07-30 10:52:00.781958: dc_card_n_hex:1,len:0
2025-07-30 10:52:00.781958: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:00.960678: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.041974: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.120682: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.200763: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.281954: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.361124: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.428690: iReadCardBas ret:4294967294
2025-07-30 10:52:01.428690: 无卡
2025-07-30 10:52:01.428690: 无卡
2025-07-30 10:52:01.437936: dc_config_card:0
2025-07-30 10:52:01.441165: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.453540: dc_card_n_hex:1,len:0
2025-07-30 10:52:01.454542: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:01.522010: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.601325: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.680061: AuthView: 检测到 1 个人脸
2025-07-30 10:52:01.761167: AuthView: 检测到 1 个人脸
2025-07-30 10:52:02.100258: iReadCardBas ret:4294967294
2025-07-30 10:52:02.100258: 无卡
2025-07-30 10:52:02.100258: 无卡
2025-07-30 10:52:02.101254: ready error :HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:52:02.101254: ready done
2025-07-30 10:52:02.101254: open reader readerType ：2 ret：-1
2025-07-30 10:52:02.101254: changeType:ReaderErrorType.openFail
2025-07-30 10:52:02.101254: 读卡器状态变化: ReaderErrorType.openFail
2025-07-30 10:52:02.101254: 读卡器连接失败，尝试重新连接
2025-07-30 10:52:02.101254: 处理读卡器连接错误
2025-07-30 10:52:02.102251: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
2025-07-30 10:52:02.109233: dc_config_card:0
2025-07-30 10:52:02.125191: dc_card_n_hex:1,len:0
2025-07-30 10:52:02.126189: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:02.241277: AuthView: 检测到 1 个人脸
2025-07-30 10:52:02.322027: AuthView: 检测到 1 个人脸
2025-07-30 10:52:02.400968: AuthView: 检测到 1 个人脸
2025-07-30 10:52:02.516152: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:52:02.516)
2025-07-30 10:52:02.516152: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:52:02.516)
2025-07-30 10:52:02.517120: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:52:02.517120: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:52:02.517120: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:52:02.517120: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 137.60494995117188, 数据大小: 0 bytes
2025-07-30 10:52:02.517120: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:52:02.517120: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:52:02.517120: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 3572 bytes, 实际置信度: 137.60494995117188
2025-07-30 10:52:02.517120: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:52:02.517120: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:52:02.517120: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:52:02.517120: ⏹️ 停止人脸捕获轮询
2025-07-30 10:52:02.517120: 暂停人脸轮询，开始认证流程
2025-07-30 10:52:02.631840: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:52:02.632812: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "21f403132aa3cb64538f373d7536a8d7",
		"log_id" : "1753843922632",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 71.65,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:52:02.632812: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:52:02.632812: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=71.65
2025-07-30 10:52:02.632812: 人脸识别成功，得分: 71.65，用户ID: 111111
2025-07-30 10:52:02.632812: 🚀 FaceRecognitionAuthService: 发送认证结果到流: AuthMethod.face - AuthStatus.success
2025-07-30 10:52:02.771521: iReadCardBas ret:4294967294
2025-07-30 10:52:02.772524: 无卡
2025-07-30 10:52:02.772524: 无卡
2025-07-30 10:52:02.773550: dc_config_card:0
2025-07-30 10:52:02.789879: dc_card_n_hex:1,len:0
2025-07-30 10:52:02.789879: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:02.961968: AuthView: 检测到 1 个人脸
2025-07-30 10:52:03.361361: AuthView: 检测到 1 个人脸
2025-07-30 10:52:03.436134: iReadCardBas ret:4294967294
2025-07-30 10:52:03.437134: 无卡
2025-07-30 10:52:03.437134: 无卡
2025-07-30 10:52:03.445137: dc_config_card:0
2025-07-30 10:52:03.461825: dc_card_n_hex:1,len:0
2025-07-30 10:52:03.461825: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:03.601452: AuthView: 检测到 1 个人脸
2025-07-30 10:52:04.000537: AuthView: 检测到 1 个人脸
2025-07-30 10:52:04.102155: 尝试重新配置读卡器
2025-07-30 10:52:04.103152: 添加设备配置: 读者证认证 -> 1个设备
2025-07-30 10:52:04.103152: 添加设备配置: 人脸识别认证 -> 1个设备
2025-07-30 10:52:04.103152: 添加设备配置: 社保卡认证 -> 1个设备
2025-07-30 10:52:04.103152: 验证读卡器配置: 类型=10, 解码器=14443AStd
2025-07-30 10:52:04.103152: 添加有效设备: type=10, id=10
2025-07-30 10:52:04.104156: 验证读卡器配置: 类型=2, 解码器=不解析
2025-07-30 10:52:04.104156: 添加有效设备: type=2, id=2
2025-07-30 10:52:04.104156: 验证读卡器配置: 类型=13, 解码器=不解析
2025-07-30 10:52:04.104156: 添加有效设备: type=13, id=13
2025-07-30 10:52:04.104156: 总共加载了3个设备配置
2025-07-30 10:52:04.104156: stopInventory newPort:SendPort
2025-07-30 10:52:04.107141: iReadCardBas ret:4294967294
2025-07-30 10:52:04.107141: 无卡
2025-07-30 10:52:04.108139: 无卡
2025-07-30 10:52:04.108139: subThread :ReaderCommand.stopInventory
2025-07-30 10:52:04.108139: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:52:04.205790: 发送 关闭 阅读器newPort:SendPort
2025-07-30 10:52:04.205790: 读卡器连接已完全关闭
2025-07-30 10:52:04.205790: changeReaders
2025-07-30 10:52:04.205790: createIsolate isOpen:true,isOpening:false
2025-07-30 10:52:04.205790: open():SendPort
2025-07-30 10:52:04.206763: untilDetcted():SendPort
2025-07-30 10:52:04.206763: 读卡器配置完成，共 3 个设备
2025-07-30 10:52:04.206763: subThread :ReaderCommand.close
2025-07-30 10:52:04.206763: commandRsp:ReaderCommand.close
2025-07-30 10:52:04.206763: cacheUsedReaders:({frmHandle: null, readerSetting: {info: {decoderType: 14443AStd, cardType: typeB, index: 1, mode: USB模式}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 10, type: 3, readerType: 10, selectedCardType: M1卡, extras: [{blockNum: 0, sectionNum: 1, password: null}]}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析, domain: 172.16.1.103:9091, account: syncDev, psw: syncDev123!, faceDomain: http://10.80.255.253:9000, type: 大连图书馆}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 2, type: 3, readerType: 2, selectedCardType: null, extras: null}}, {frmHandle: null, readerSetting: {info: {decoderType: 不解析}, coderConfig: {bookCode: , shelfCode: , readerCardCode: , cdCode: , prefixStr: , suffixStr: , replaceRuleStr: }, id: 13, type: 3, readerType: 13, selectedCardType: 华大社保卡, extras: null}})
2025-07-30 10:52:04.206763: close:T10Bridge
2025-07-30 10:52:04.206763: dc_exit:0
2025-07-30 10:52:04.206763: close:HWFaceBridge
2025-07-30 10:52:04.207760: isConnect:false
2025-07-30 10:52:04.207760: disconnect
2025-07-30 10:52:04.207760: close:HD100SSBridge
2025-07-30 10:52:04.207760: iCloseCard error: Invalid argument(s): Failed to lookup symbol 'iCloseCard': error code 127
2025-07-30 10:52:04.207760: HD100SS close result: -1
2025-07-30 10:52:04.207760: close:done
2025-07-30 10:52:04.207760: changeType:ReaderErrorType.closeSuccess
2025-07-30 10:52:04.207760: 读卡器状态变化: ReaderErrorType.closeSuccess
2025-07-30 10:52:04.207760: already close all reader
2025-07-30 10:52:04.208758: subThread :ReaderCommand.readerList
2025-07-30 10:52:04.208758: commandRsp:ReaderCommand.readerList
2025-07-30 10:52:04.208758: readerList：3,readerSetting：3
2025-07-30 10:52:04.208758: cacheUsedReaders:3
2025-07-30 10:52:04.208758: subThread :ReaderCommand.open
2025-07-30 10:52:04.208758: commandRsp:ReaderCommand.open
2025-07-30 10:52:04.213743: dc_init:0xb4 100
2025-07-30 10:52:04.213743: open reader readerType ：10 ret：0
2025-07-30 10:52:04.213743: wsurl:172.16.1.103:9091
2025-07-30 10:52:04.214754: subThread :ReaderCommand.untilDetected
2025-07-30 10:52:04.214754: commandRsp:ReaderCommand.untilDetected
2025-07-30 10:52:04.634693: 认证冷却期结束，重新开始人脸识别监听
2025-07-30 10:52:04.634693: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:52:04.634693: 认证完成，恢复人脸轮询
2025-07-30 10:52:04.717065: dc_config_card:0
2025-07-30 10:52:04.733269: dc_card_n_hex:1,len:0
2025-07-30 10:52:04.733269: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:05.380221: iReadCardBas ret:4294967294
2025-07-30 10:52:05.380221: 无卡
2025-07-30 10:52:05.380221: 无卡
2025-07-30 10:52:05.381219: dc_config_card:0
2025-07-30 10:52:05.397176: dc_card_n_hex:1,len:0
2025-07-30 10:52:05.397176: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:06.043454: iReadCardBas ret:4294967294
2025-07-30 10:52:06.043454: 无卡
2025-07-30 10:52:06.043454: 无卡
2025-07-30 10:52:06.045450: dc_config_card:0
2025-07-30 10:52:06.061407: dc_card_n_hex:1,len:0
2025-07-30 10:52:06.061407: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:06.636335: 🔍 [FaceCapturePolling] 轮询检查新人脸数据... (时间: 10:52:06.636)
2025-07-30 10:52:06.636335: 🔍 [FaceCapturePolling] C++返回has_new_face_capture结果: 有新数据 (时间: 10:52:06.636)
2025-07-30 10:52:06.636335: ✅ [FaceCapturePolling] 检测到新数据，开始获取人脸信息...
2025-07-30 10:52:06.636335: 📞 [FaceCapturePolling] 调用C++的get_face_capture_info函数...
2025-07-30 10:52:06.636335: 📞 [FaceCapturePolling] get_face_capture_info返回结果: 成功
2025-07-30 10:52:06.637335: 📊 [FaceCapturePolling] 获取到人脸信息 - 置信度: 132.96775817871094, 数据大小: 0 bytes
2025-07-30 10:52:06.637335: 📞 [FaceCapturePolling] 开始调用C++的get_latest_face_capture函数获取图像数据...
2025-07-30 10:52:06.637335: 📞 [FaceCapturePolling] get_latest_face_capture返回结果: 成功
2025-07-30 10:52:06.637335: 📊 [FaceCapturePolling] 获取到图像数据 - 实际大小: 2332 bytes, 实际置信度: 132.96775817871094
2025-07-30 10:52:06.637335: ✅ [FaceCapturePolling] 图像数据复制到Dart完成，准备调用onFaceCaptured回调
2025-07-30 10:52:06.637335: 🚀 [FaceCapturePolling] 调用onFaceCaptured回调，传递图像数据和置信度
2025-07-30 10:52:06.637335: 🎯 检测到新的人脸数据，开始获取...
2025-07-30 10:52:06.637335: ⏹️ 停止人脸捕获轮询
2025-07-30 10:52:06.637335: 暂停人脸轮询，开始认证流程
2025-07-30 10:52:06.757276: ✅ [FaceCapturePolling] onFaceCaptured回调调用完成
2025-07-30 10:52:06.757276: 🔍 百度SDK识别结果: {
	"data" : 
	{
		"face_token" : "2ec1dfa74175360ccaa22161b198e630",
		"log_id" : "1753843926756",
		"result" : 
		[
			{
				"group_id" : "hh_group",
				"score" : 59.79,
				"user_id" : "111111"
			}
		],
		"result_num" : 1
	},
	"errno" : 0,
	"msg" : "success"
}
2025-07-30 10:52:06.757276: ✅ 使用百度SDK格式解析，找到 1 个识别结果
2025-07-30 10:52:06.757276: 🎯 识别结果 1: 用户ID=111111, 分组=hh_group, 得分=59.79
2025-07-30 10:52:06.757276: 🔄 开始人脸捕获轮询，间隔: 2000ms
2025-07-30 10:52:06.757276: 认证完成，恢复人脸轮询
2025-07-30 10:52:06.760267: iReadCardBas ret:4294967294
2025-07-30 10:52:06.760267: 无卡
2025-07-30 10:52:06.760267: 无卡
2025-07-30 10:52:06.760267: dc_config_card:0
2025-07-30 10:52:06.760267: dc_card_n_hex:1,len:0
2025-07-30 10:52:06.761265: iReadCardBas: start ,isOpenComPort:false
2025-07-30 10:52:07.183361: CameraPreviewWidget: 开始清理资源...
2025-07-30 10:52:07.184370: CameraPreviewWidget: 停止帧捕获定时器
2025-07-30 10:52:07.184370: CameraPreviewWidget: 资源清理完成
2025-07-30 10:52:07.184370: 停止所有认证方式监听
2025-07-30 10:52:07.184370: 停止人脸识别认证监听
2025-07-30 10:52:07.184370: 停止人脸认证监听
2025-07-30 10:52:07.184370: 🛑 停止人脸识别监听...
2025-07-30 10:52:07.184370: ⏹️ 停止人脸捕获轮询
2025-07-30 10:52:07.184370: ✅ 人脸识别监听已停止
2025-07-30 10:52:07.184370: 停止读者证认证监听
2025-07-30 10:52:07.184370: 停止读卡器认证监听
2025-07-30 10:52:07.185356: 停止社保卡认证监听
2025-07-30 10:52:07.185356: 停止读卡器认证监听
2025-07-30 10:52:07.185356: 开始清理门锁连接...
2025-07-30 10:52:07.185356: 门锁继电器已断开连接
2025-07-30 10:52:07.185356: 门锁连接已断开: COM1
2025-07-30 10:52:07.185356: 门锁连接清理完成
2025-07-30 10:52:07.185356: stopInventory newPort:SendPort
2025-07-30 10:52:07.185356: FaceRecognitionAuthService监听已停止
2025-07-30 10:52:07.186353: 已移除读卡器状态监听器
2025-07-30 10:52:07.186353: 已移除标签数据监听器
2025-07-30 10:52:07.186353: 所有卡片监听器已移除
2025-07-30 10:52:07.186353: 没有活跃的读卡器连接需要暂停
2025-07-30 10:52:07.186353: 已移除读卡器状态监听器
2025-07-30 10:52:07.186353: 已移除标签数据监听器
2025-07-30 10:52:07.186353: 所有卡片监听器已移除
2025-07-30 10:52:07.186353: 没有活跃的读卡器连接需要暂停
2025-07-30 10:52:07.186353: 串口数据监听结束
2025-07-30 10:52:07.187351: 认证结果流订阅已取消
2025-07-30 10:52:07.187351: 读卡器认证监听已停止（连接保持）
2025-07-30 10:52:07.187351: 读者证认证服务监听已停止
2025-07-30 10:52:07.187351: 读卡器认证监听已停止（连接保持）
2025-07-30 10:52:07.187351: 社保卡认证服务监听已停止
2025-07-30 10:52:07.187351: 人脸认证监听已停止
2025-07-30 10:52:07.187351: 人脸识别认证服务监听已停止
2025-07-30 10:52:07.187351: 多认证管理器状态变更: idle
2025-07-30 10:52:07.187351: 所有认证方式监听已停止
2025-07-30 10:52:07.372118: iReadCardBas ret:4294967294
2025-07-30 10:52:07.372118: 无卡
2025-07-30 10:52:07.372118: 无卡
2025-07-30 10:52:07.373120: subThread :ReaderCommand.stopInventory
2025-07-30 10:52:07.373120: commandRsp:ReaderCommand.stopInventory
2025-07-30 10:52:07.373120: ready error :HttpException: Connection closed before full header was received, uri = http://172.16.1.103:9091
2025-07-30 10:52:07.373120: ready done
2025-07-30 10:52:07.373120: open reader readerType ：2 ret：-1
2025-07-30 10:52:07.374115: changeType:ReaderErrorType.openFail
2025-07-30 10:52:07.374115: open Concurrent modification during iteration: Instance(length:0) of '_GrowableList'.
