import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../face_detector.dart';
import '../../auth/services/face_recognition_auth_service.dart';

/// 摄像头预览组件 - 性能优化版本
///
/// 优化内容：
/// 1. 移除了系统监控定时器和自适应质量控制，减少CPU占用
/// 2. 使用 ValueNotifier 替代频繁的 setState，减少UI重建
/// 3. 使用 ValueListenableBuilder 和 RepaintBoundary 优化渲染性能
/// 4. 简化性能参数，使用固定值避免动态调整开销
/// 5. 合并状态更新，减少不必要的UI刷新
///
/// 当前设置：
/// - 帧捕获间隔: 50ms (20 FPS) - 提升流畅度
/// - 人脸检测间隔: 80ms - 平衡性能和准确性
class CameraPreviewWidget extends StatefulWidget {
  final double width;
  final double height;
  final bool enableFaceDetection;
  final Function(Uint8List?)? onFrameUpdate;
  final Function(List<Map<dynamic, dynamic>>)? onFacesDetected;
  final bool autoStart;
  final int? cameraId;
  final bool enableFaceRecognition; // 新增：是否启用人脸识别
  final Function(String userId, String? userInfo)? onFaceRecognized; // 新增：人脸识别成功回调
  
  const CameraPreviewWidget({
    Key? key,
    required this.width,
    required this.height,
    this.enableFaceDetection = false,
    this.onFrameUpdate,
    this.onFacesDetected,
    this.autoStart = true,
    this.cameraId,
    this.enableFaceRecognition = false,
    this.onFaceRecognized,
  }) : super(key: key);

  @override
  State<CameraPreviewWidget> createState() => _CameraPreviewWidgetState();
}

class _CameraPreviewWidgetState extends State<CameraPreviewWidget>
    with WidgetsBindingObserver {

  // 核心状态变量 - 优化版本
  bool _isProcessing = false;
  bool _isDetecting = false;
  bool _initialized = false;
  Timer? _frameTimer;
  Timer? _detectionTimer;

  // 使用 ValueNotifier 优化频繁更新的数据
  final ValueNotifier<Uint8List?> _imageDataNotifier = ValueNotifier<Uint8List?>(null);
  final ValueNotifier<List<Map<dynamic, dynamic>>> _facesNotifier = ValueNotifier<List<Map<dynamic, dynamic>>>([]);

  // 简化的性能参数
  static const int _jpegQuality = 60;
  static const int _frameInterval = 60; // 调整为50ms (20fps)
  static const int _detectionInterval = 80; // 保持80ms检测间隔
  static const bool _useHaarDetector = false;

  // 摄像头状态
  bool _isCameraRunning = false;
  int _currentCameraId = 0;

  // 人脸识别服务
  FaceRecognitionAuthService? _faceRecognitionService;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    
    // 设置摄像头ID
    if (widget.cameraId != null) {
      _currentCameraId = widget.cameraId!;
    }
    
    // 自动启动摄像头
    if (widget.autoStart) {
      _initializeCamera();
    }

    // 如果启用人脸识别，初始化人脸识别服务
    if (widget.enableFaceRecognition) {
      _initializeFaceRecognitionService();
    }
  }

  /// 初始化摄像头 - 复用 face_detector_app.dart 的逻辑
  Future<void> _initializeCamera() async {
    try {
      print("📷 CameraPreviewWidget: 检查摄像头检测器初始化状态...");
      
      // 检查是否已初始化，避免重复初始化
      if (!FaceDetector.isInitialized) {
        print("🔧 CameraPreviewWidget: 开始初始化摄像头检测器...");
        final initStartTime = DateTime.now();
        await FaceDetector.initialize();
        final initEndTime = DateTime.now();
        final initDuration = initEndTime.difference(initStartTime).inMilliseconds;
        print("✅ CameraPreviewWidget: 摄像头检测器初始化完成，耗时: ${initDuration}ms");
      } else {
        print("✅ CameraPreviewWidget: 摄像头检测器已初始化，跳过初始化步骤");
      }

      print("📹 CameraPreviewWidget: 启动摄像头 $_currentCameraId...");
      final cameraStartTime = DateTime.now();
      final cameraStarted = await FaceDetector.startCameraWithID(_currentCameraId);
      final cameraEndTime = DateTime.now();
      final cameraDuration = cameraEndTime.difference(cameraStartTime).inMilliseconds;
      print("${cameraStarted ? '✅' : '❌'} CameraPreviewWidget: 摄像头启动${cameraStarted ? '成功' : '失败'}，耗时: ${cameraDuration}ms");
      
      if (cameraStarted) {
        // 设置固定参数
        FaceDetector.setJpegQuality(_jpegQuality);
        FaceDetector.setUseHaarDetector(_useHaarDetector);
        FaceDetector.setSmoothingFactor(0.2);
        FaceDetector.setProcessingInterval(30);

        // 预加载模型
        await _preloadModel();

        setState(() {
          _initialized = true;
          _isCameraRunning = true;
        });

        // 开始帧捕获
        _startFrameCapture();

        // 如果启用人脸检测，开始检测
        if (widget.enableFaceDetection) {
          _startDetection();
        }

        print("✅ CameraPreviewWidget: 摄像头初始化完成");
      } else {
        print("❌ CameraPreviewWidget: 摄像头启动失败");
      }
    } catch (e) {
      print("❌ CameraPreviewWidget: 初始化摄像头时出错: $e");
    }
  }

  /// 预加载模型 - 与原始实现一致
  Future<void> _preloadModel() async {
    try {
      debugPrint('CameraPreviewWidget: 预加载DNN模型...');
      final dynamic result = FaceDetector.detectFacesOnly();
      debugPrint('CameraPreviewWidget: DNN模型预加载完成，获取到 ${result is List ? result.length : 0} 个结果');
    } catch (e) {
      debugPrint('CameraPreviewWidget: DNN模型预加载失败: $e');
    }
  }

  /// 初始化人脸识别服务
  Future<void> _initializeFaceRecognitionService() async {
    if (!widget.enableFaceRecognition) return;

    try {
      debugPrint('CameraPreviewWidget: 开始初始化人脸识别服务...');

      // 确保之前的服务已完全清理
      if (_faceRecognitionService != null) {
        debugPrint('CameraPreviewWidget: 清理之前的人脸识别服务...');
        await _faceRecognitionService!.stopListening();
        _faceRecognitionService!.dispose();
        _faceRecognitionService = null;

        // 给一点时间让资源完全释放
        await Future.delayed(const Duration(milliseconds: 100));
      }

      _faceRecognitionService = FaceRecognitionAuthService();
      await _faceRecognitionService!.initialize(
        onFaceRecognized: (userId, userInfo) {
          // 调用外部回调
          widget.onFaceRecognized?.call(userId, userInfo);
        },
        onError: (error) {
          debugPrint('CameraPreviewWidget: 人脸识别错误: $error');
        },
      );

      // 开始监听人脸识别
      await _faceRecognitionService!.startListening();
      debugPrint('CameraPreviewWidget: 人脸识别服务初始化成功');
    } catch (e) {
      debugPrint('CameraPreviewWidget: 人脸识别服务初始化失败: $e');
    }
  }

  // 移除所有监控相关方法，简化组件

  /// 开始帧捕获 - 简化版本
  void _startFrameCapture() {
    _stopFrameCapture();

    _frameTimer = Timer.periodic(Duration(milliseconds: _frameInterval), (_) {
      if (_isProcessing) {
        return; // 跳过当前帧
      }
      _getFrameFromDetector();
    });

    debugPrint('CameraPreviewWidget: 帧捕获已启动，间隔: ${_frameInterval}ms');
  }

  /// 停止帧捕获
  void _stopFrameCapture() {
    if (_frameTimer != null) {
      debugPrint('CameraPreviewWidget: 停止帧捕获定时器');
      _frameTimer?.cancel();
      _frameTimer = null;
    }
  }

  /// 从检测器获取帧数据 - 优化版本
  Future<bool> _getFrameFromDetector() async {
    try {
      _isProcessing = true;
      final Uint8List? imageData = await FaceDetector.getJpegImageData();
      if (imageData == null || imageData.isEmpty) {
        _isProcessing = false;
        return false;
      }

      // 使用 ValueNotifier 更新图像数据，避免 setState
      _imageDataNotifier.value = imageData;
      _isProcessing = false;

      // 调用回调函数
      widget.onFrameUpdate?.call(imageData);

      return true;
    } catch (e) {
      debugPrint('CameraPreviewWidget: 获取帧失败: $e');
      _isProcessing = false;
      return false;
    }
  }

  /// 开始人脸检测 - 简化版本
  void _startDetection() {
    _stopDetection();

    _isDetecting = true;

    _detectionTimer = Timer.periodic(Duration(milliseconds: _detectionInterval), (timer) {
      if (!widget.enableFaceDetection || !mounted) {
        _stopDetection();
        return;
      }

      _detectFacesInCurrentFrame();
    });

    debugPrint('CameraPreviewWidget: 人脸检测器已启动，检测间隔: ${_detectionInterval}ms');
  }

  /// 停止人脸检测
  void _stopDetection() {
    _isDetecting = false;
    _detectionTimer?.cancel();
    _detectionTimer = null;
  }

  /// 检测当前帧中的人脸 - 优化版本
  Future<bool> _detectFacesInCurrentFrame() async {
    if (!widget.enableFaceDetection || !mounted) return false;

    _isDetecting = true;

    try {
      final List<Map<dynamic, dynamic>> faces = FaceDetector.detectFacesOnly();

      // 使用 ValueNotifier 更新人脸数据，避免 setState
      _facesNotifier.value = faces;
      _isDetecting = false;

      // 调用回调函数
      widget.onFacesDetected?.call(faces);

      return true;
    } catch (e) {
      debugPrint('CameraPreviewWidget: 人脸检测失败: $e');
      _isDetecting = false;
      return false;
    }
  }

  // 移除自适应质量控制方法

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 应用进入后台时停止捕获帧，恢复时重新开始
    if (state == AppLifecycleState.paused) {
      _stopFrameCapture();
      _stopDetection();
    } else if (state == AppLifecycleState.resumed) {
      if (_initialized && mounted) {
        _startFrameCapture();
        if (widget.enableFaceDetection) {
          _startDetection();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_initialized) {
      return Container(
        width: widget.width,
        height: widget.height,
        decoration: const BoxDecoration(
          color: Color(0xFF12215F),
          shape: BoxShape.rectangle,
        ),
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4F83FC)),
          ),
        ),
      );
    }

    // 使用 ValueListenableBuilder 优化性能，避免不必要的重建
    return ValueListenableBuilder<Uint8List?>(
      valueListenable: _imageDataNotifier,
      builder: (context, imageData, child) {
        if (imageData == null) {
          return Container(
            width: widget.width,
            height: widget.height,
            decoration: const BoxDecoration(
              color: Color(0xFF12215F),
              shape: BoxShape.rectangle,
            ),
            child: const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4F83FC)),
              ),
            ),
          );
        }

        return RepaintBoundary(
          child:  SizedBox(
            width: widget.width,
            height: widget.height,
            child: Image.memory(
              imageData,
              width: widget.width,
              height: widget.height,
              fit: BoxFit.cover,
              gaplessPlayback: true,
              errorBuilder: (context, error, stackTrace) {
                debugPrint('CameraPreviewWidget: 图像显示错误: $error');
                return Container(
                  width: widget.width,
                  height: widget.height,
                  decoration: const BoxDecoration(
                    color: Color(0xFF12215F),
                    shape: BoxShape.rectangle,
                  ),
                  child: const Center(
                    child: Icon(
                      Icons.error,
                      color: Colors.red,
                      size: 48,
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    debugPrint('CameraPreviewWidget: 开始清理资源...');

    WidgetsBinding.instance.removeObserver(this);
    _stopFrameCapture();
    _stopDetection();

    // 清理 ValueNotifier
    _imageDataNotifier.dispose();
    _facesNotifier.dispose();

    // 彻底清理人脸识别服务
    if (_faceRecognitionService != null) {
      debugPrint('CameraPreviewWidget: 停止人脸识别服务...');
      // 先停止监听，再dispose
      _faceRecognitionService!.stopListening().then((_) {
        _faceRecognitionService!.dispose();
        _faceRecognitionService = null;
        debugPrint('CameraPreviewWidget: 人脸识别服务已清理');
      }).catchError((e) {
        debugPrint('CameraPreviewWidget: 清理人脸识别服务时出错: $e');
        _faceRecognitionService!.dispose();
        _faceRecognitionService = null;
      });
    }

    // 只有在这是最后一个使用摄像头的组件时才停止摄像头
    // 这里可以添加引用计数逻辑，暂时注释掉以避免影响其他组件
    // FaceDetector.stopCamera();

    debugPrint('CameraPreviewWidget: 资源清理完成');
    super.dispose();
  }

  /// 公共方法：手动启动摄像头
  Future<void> startCamera() async {
    if (!_initialized) {
      await _initializeCamera();
    }
  }

  /// 公共方法：停止摄像头
  void stopCamera() {
    _stopFrameCapture();
    _stopDetection();
    setState(() {
      _initialized = false;
      _isCameraRunning = false;
    });
    _imageDataNotifier.value = null;
  }

  /// 公共方法：获取当前帧数据
  Uint8List? getCurrentFrame() {
    return _imageDataNotifier.value;
  }

  /// 公共方法：获取当前人脸数据
  List<Map<dynamic, dynamic>> getCurrentFaces() {
    return _facesNotifier.value;
  }

  /// 公共方法：获取基本状态信息
  Map<String, dynamic> getStatusInfo() {
    return {
      'initialized': _initialized,
      'cameraRunning': _isCameraRunning,
      'isProcessing': _isProcessing,
      'isDetecting': _isDetecting,
      'frameInterval': _frameInterval,
      'detectionInterval': _detectionInterval,
    };
  }
}
