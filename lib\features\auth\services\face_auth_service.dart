import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seaface/seaface.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';

import '../models/auth_result.dart';
import 'auth_service_interface.dart';
import 'face_recognition_auth_service.dart';

/// 人脸认证服务
/// 负责人脸识别的监听和认证处理
/// 作为多认证管理器和FaceRecognitionAuthService之间的桥梁
class FaceAuthService implements AuthServiceInterface {
  // 状态管理
  bool _isInitialized = false;
  bool _isListening = false;
  String? _errorMessage;

  // 人脸识别服务实例
  FaceRecognitionAuthService? _faceRecognitionService;
  StreamSubscription? _faceRecognitionSubscription;

  // 人脸检测相关
  Timer? _faceDetectionTimer;
  StreamSubscription? _cameraSubscription;
  dynamic _cameraProvider;

  // 认证结果流
  final StreamController<AuthResult> _authResultController =
      StreamController<AuthResult>.broadcast();

  @override
  Stream<AuthResult> get authResultStream => _authResultController.stream;

  @override
  bool get isListening => _isListening;

  @override
  bool get isInitialized => _isInitialized;

  @override
  String? get errorMessage => _errorMessage;

  @override
  Future<void> initialize(BuildContext context) async {
    if (_isInitialized) {
      return;
    }

    try {
      print('初始化人脸认证服务');

      // 创建并初始化FaceRecognitionAuthService
      _faceRecognitionService = FaceRecognitionAuthService();
      await _faceRecognitionService!.initialize();

      // 监听FaceRecognitionAuthService的认证结果
      _faceRecognitionSubscription = _faceRecognitionService!.authResultStream.listen(
        (result) async {
          // 如果是人脸识别成功，需要先请求读者信息获取真实姓名
          if (result.status == AuthStatus.success && result.method == AuthMethod.face) {
            print('🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...');
            await _handleFaceRecognitionSuccess(result);
          } else {
            // 其他情况直接转发
            _authResultController.add(result);
          }
        },
        onError: (error) {
          _setError('人脸识别错误: $error');
        },
      );

      _isInitialized = true;
      _clearError();
      print('人脸认证服务初始化成功');
    } catch (e) {
      _setError('人脸认证服务初始化失败: $e');
      throw e;
    }
  }

  @override
  Future<void> startListening() async {
    if (_isListening) {
      print('人脸认证已在监听中，检查认证结果流连接状态...');

      // 检查认证结果流是否正常工作
      if (_faceRecognitionSubscription == null || _faceRecognitionSubscription!.isPaused) {
        print('认证结果流连接异常，重新建立连接...');
        // 重新建立认证结果流连接
        _faceRecognitionSubscription?.cancel();
        _faceRecognitionSubscription = _faceRecognitionService!.authResultStream.listen(
          (result) async {
            // 如果是人脸识别成功，需要先请求读者信息获取真实姓名
            if (result.status == AuthStatus.success && result.method == AuthMethod.face) {
              print('🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...');
              await _handleFaceRecognitionSuccess(result);
            } else {
              // 其他情况直接转发
              print('人脸认证服务转发认证结果: ${result.method} - ${result.status}');
              _authResultController.add(result);
            }
          },
          onError: (error) {
            _setError('人脸识别错误: $error');
          },
        );
        print('认证结果流连接已重新建立');
      }
      return;
    }

    try {
      print('开始人脸认证监听');

      // 如果服务未初始化或底层服务不存在，尝试重新初始化
      if (!_isInitialized || _faceRecognitionService == null) {
        print('人脸认证服务未初始化，尝试自动初始化...');
        try {
          // 清理旧的服务实例
          if (_faceRecognitionService != null) {
            _faceRecognitionService!.dispose();
            _faceRecognitionService = null;
          }
          _faceRecognitionSubscription?.cancel();
          _faceRecognitionSubscription = null;

          // 重新创建并初始化服务
          _faceRecognitionService = FaceRecognitionAuthService();
          await _faceRecognitionService!.initialize();

          // 重新监听认证结果
          _faceRecognitionSubscription = _faceRecognitionService!.authResultStream.listen(
            (result) async {
              // 如果是人脸识别成功，需要先请求读者信息获取真实姓名
              if (result.status == AuthStatus.success && result.method == AuthMethod.face) {
                print('🔍 FaceAuthService: 收到人脸识别成功结果，开始请求读者信息...');
                await _handleFaceRecognitionSuccess(result);
              } else {
                // 其他情况直接转发
                _authResultController.add(result);
              }
            },
            onError: (error) {
              _setError('人脸识别错误: $error');
            },
          );

          _isInitialized = true;
          print('人脸认证服务自动初始化成功');
        } catch (initError) {
          print('人脸认证服务自动初始化失败: $initError');
          _setError('人脸认证服务自动初始化失败: $initError');
          _isListening = false;
          return; // 初始化失败，直接返回
        }
      }

      // 启动FaceRecognitionAuthService的监听
      await _faceRecognitionService!.startListening();

      _isListening = true;
      _clearError();
      print('人脸认证监听启动成功');
    } catch (e) {
      _setError('启动人脸认证监听失败: $e');
      print('人脸认证启动失败，但不影响其他认证方式: $e');

      // 不抛出异常，允许其他认证方式继续工作
      // 但标记为未在监听状态
      _isListening = false;
    }
  }

  @override
  Future<void> stopListening() async {
    if (!_isListening) {
      return;
    }

    try {
      print('停止人脸认证监听');

      // 停止FaceRecognitionAuthService的监听
      if (_faceRecognitionService != null) {
        await _faceRecognitionService!.stopListening();
        print('FaceRecognitionAuthService监听已停止');
      }

      // 取消认证结果流订阅
      await _faceRecognitionSubscription?.cancel();
      _faceRecognitionSubscription = null;
      print('认证结果流订阅已取消');

      // 停止人脸检测定时器
      _faceDetectionTimer?.cancel();
      _faceDetectionTimer = null;

      // 停止相机订阅
      await _cameraSubscription?.cancel();
      _cameraSubscription = null;

      // 停止相机
      await _stopCamera();

      _isListening = false;
      _clearError();
      print('人脸认证监听已停止');
    } catch (e) {
      // 增强错误处理
      String errorMessage = '停止人脸认证监听失败: $e';
      
      // 对特定错误类型进行处理
      if (e.toString().contains('WebSocket') || 
          e.toString().contains('SocketException') ||
          e.toString().contains('远程计算机拒绝网络连接')) {
        errorMessage = 'WebSocket连接错误，但人脸认证监听已停止';
        print('检测到WebSocket连接错误，忽略并继续停止流程');
        _isListening = false; // 强制设置为已停止
        return; // 不抛出异常
      } else if (e.toString().contains('Failed to lookup symbol') || 
                 e.toString().contains('error code 127')) {
        errorMessage = '动态库符号查找失败，但人脸认证监听已停止';
        print('检测到动态库符号查找失败，忽略并继续停止流程');
        _isListening = false; // 强制设置为已停止
        return; // 不抛出异常
      } else if (e.toString().contains('camera') || 
                 e.toString().contains('相机')) {
        errorMessage = '相机停止失败，但人脸认证监听已停止';
        print('检测到相机停止错误，忽略并继续停止流程');
        _isListening = false; // 强制设置为已停止
        return; // 不抛出异常
      }
      
      _setError(errorMessage);
      print('停止人脸认证监听出错: $e');
      
      // 对于其他类型的错误，仍然设置为已停止状态
      _isListening = false;
    }
  }

  @override
  Future<void> reset() async {
    await stopListening();
    await Future.delayed(const Duration(milliseconds: 500));
    if (_isInitialized) {
      await startListening();
    }
  }

  @override
  void dispose() {
    stopListening();
    _authResultController.close();
    _cameraProvider = null;
    _isInitialized = false;
  }

  @override
  Map<String, dynamic> getStatus() {
    return {
      'service': 'FaceAuthService',
      'initialized': _isInitialized,
      'listening': _isListening,
      'error': _errorMessage,
      'has_camera_provider': _cameraProvider != null,
    };
  }

  /// 初始化相机
  Future<void> _initializeCamera() async {
    try {
      // 相机Provider已在应用启动时初始化，这里只需要获取引用
      print('初始化人脸识别相机');
      
      // SFCameraProvider在应用启动时已经初始化
      // 这里不需要额外的初始化操作
    } catch (e) {
      throw Exception('相机初始化失败: $e');
    }
  }

  /// 启动相机
  Future<void> _startCamera() async {
    try {
      if (_cameraProvider != null) {
        // SFCameraProvider的相机应该已经在运行
        // 这里可以添加监听器来监听状态变化
        _cameraProvider!.addListener(_onCameraStateChanged);
      }
    } catch (e) {
      throw Exception('启动相机失败: $e');
    }
  }

  /// 停止相机
  Future<void> _stopCamera() async {
    try {
      if (_cameraProvider != null) {
        // 移除监听器
        _cameraProvider!.removeListener(_onCameraStateChanged);
      }
    } catch (e) {
      print('停止相机失败: $e');
    }
  }

  /// 相机状态变化监听
  void _onCameraStateChanged() {
    // 当相机状态变化时触发
    if (_isListening) {
      // 可以在这里触发人脸检测
      _performFaceDetection();
    }
  }

  /// 开始人脸检测
  void _startFaceDetection() {
    _faceDetectionTimer?.cancel();
    
    // 每500毫秒进行一次人脸检测
    _faceDetectionTimer = Timer.periodic(
      const Duration(milliseconds: 500),
      (_) => _performFaceDetection(),
    );
  }

  /// 相机数据接收处理
  void _onCameraDataReceived(dynamic data) {
    // 处理相机数据，更新UI显示
    // 这里的具体实现取决于seaface库的数据格式
  }

  /// 执行人脸检测
  void _performFaceDetection() async {
    if (!_isListening || _cameraProvider == null) {
      return;
    }

    try {
      // 获取当前相机帧数据
      final imageData = _cameraProvider!.imageData;
      if (imageData?.devices?.isNotEmpty != true) {
        return;
      }

      final deviceData = imageData!.devices!.first;
      final videoData = deviceData.recv_data?.video;
      
      if (videoData == null || videoData.isEmpty) {
        return;
      }

      // 进行人脸检测和识别
      final result = await _recognizeFace(videoData);
      if (result != null) {
        _authResultController.add(result);
      }
    } catch (e) {
      print('人脸检测错误: $e');
      // 不设置为错误，继续检测
    }
  }

  /// 人脸识别
  Future<AuthResult?> _recognizeFace(String base64Image) async {
    try {
      // 解码base64图像
      final imageBytes = base64Decode(base64Image);
      
      // 调用人脸识别API或SDK
      // 这里需要根据实际使用的人脸识别库来实现
      final recognitionResult = await _callFaceRecognitionAPI(imageBytes);
      
      if (recognitionResult != null && recognitionResult.isSuccess) {
        // 认证成功
        return AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.success,
          userId: recognitionResult.userId,
          userName: recognitionResult.userName,
        );
      } else {
        // 认证失败，但不返回失败结果，继续检测
        return null;
      }
    } catch (e) {
      print('人脸识别处理错误: $e');
      return null;
    }
  }

  /// 调用人脸识别API
  Future<FaceRecognitionResult?> _callFaceRecognitionAPI(List<int> imageBytes) async {
    try {
      // 这里应该调用实际的人脸识别API
      // 可能是本地SDK或者远程API
      // TODO: 集成真实的人脸识别API
      
      // 暂时返回null，等待真实API集成
      return null;
    } catch (e) {
      print('人脸识别API调用失败: $e');
      return null;
    }
  }

  /// 设置错误信息
  void _setError(String error) {
    _errorMessage = error;
    print('人脸认证服务错误: $error');
  }

  /// 清除错误信息
  void _clearError() {
    _errorMessage = null;
  }

  /// 处理人脸识别成功结果，请求读者信息获取真实姓名
  Future<void> _handleFaceRecognitionSuccess(AuthResult faceResult) async {
    try {
      print('🔍 开始请求读者信息: 用户ID=${faceResult.userId}');

      // 调用SIP2服务获取读者信息
      final readerInfo = await NewSip2Request.instance.getReaderInfo(
        faceResult.userId!,
        faceResult.userId!,
      ).timeout(const Duration(seconds: 10));

      if (readerInfo?.isSuccess == true && readerInfo?.ValidPatron == 'Y') {
        // 获取读者信息成功，使用真实姓名
        final successResult = AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.success,
          userId: readerInfo!.PatronIdentifier ?? faceResult.userId,
          userName: readerInfo.PersonName ?? '人脸识别用户',
          timestamp: DateTime.now(),
        );

        print('✅ 读者信息获取成功: 姓名=${readerInfo.PersonName}, ID=${readerInfo.PatronIdentifier}');
        print('🚀 FaceAuthService: 发送包含真实姓名的认证结果');
        _authResultController.add(successResult);
      } else {
        // 读者信息获取失败，发送失败结果
        final failureResult = AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.failureNoMatch,
          errorMessage: '读者信息验证失败，请联系管理员',
          timestamp: DateTime.now(),
        );

        print('❌ 读者信息获取失败，发送失败结果');
        _authResultController.add(failureResult);
      }
    } catch (e) {
      print('❌ 请求读者信息时出错: $e');

      // 发送错误结果
      final errorResult = AuthResult(
        method: AuthMethod.face,
        status: AuthStatus.failureError,
        errorMessage: '读者信息请求异常: $e',
        timestamp: DateTime.now(),
      );

      _authResultController.add(errorResult);
    }
  }
}

/// 人脸识别结果
class FaceRecognitionResult {
  final bool isSuccess;
  final String? userId;
  final String? userName;
  final double confidence;

  FaceRecognitionResult({
    required this.isSuccess,
    this.userId,
    this.userName,
    this.confidence = 0.0,
  });
} 