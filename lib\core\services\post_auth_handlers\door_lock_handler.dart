import 'package:flutter/foundation.dart';
import 'package:sea_socket/sea_socket.dart';
import 'package:seasetting/seasetting.dart';
import 'package:hardware/hardware.dart';
import '../post_auth_service.dart';

/// 门锁连接获取器 - 用于从认证页面获取连接
class AuthPageDoorLockConnections {
  static Map<String, DoorRelayManager>? _currentConnections;

  /// 设置当前认证页面的连接
  static void setConnections(Map<String, DoorRelayManager> connections) {
    _currentConnections = connections;
  }

  /// 获取指定端口的连接
  static DoorRelayManager? getConnection(String comPort) {
    return _currentConnections?[comPort];
  }

  /// 清除连接引用
  static void clearConnections() {
    _currentConnections = null;
  }
}

/// 门锁开门处理器 - 认证成功后自动开门
class DoorLockHandler implements PostAuthHandler {
  @override
  int get priority => 10; // 高优先级，在欢迎信息之前执行

  // 开门持续时间（毫秒）
  static const int _doorOpenDurationMs = 600;
  
  @override
  Future<bool> handle(Sip2PatronInfoData readerInfo, String authMethod) async {
    try {
      debugPrint('开始执行自动开门处理 - 用户: ${readerInfo.PersonName}, 认证方式: $authMethod');
      
      // 1. 获取门锁继电器配置
      final config = await _getFirstDoorLockConfig();
      if (config == null) {
        debugPrint('未找到门锁继电器配置，跳过自动开门');
        return true; // 继续执行其他处理器
      }
      
      debugPrint('使用门锁配置: ${config.name} (${config.comPort})');
      
      // 2. 执行开门操作
      final success = await _performDoorOpen(config, readerInfo);
      
      if (success) {
        debugPrint('自动开门成功 - 用户: ${readerInfo.PersonName}');
      } else {
        debugPrint('自动开门失败 - 用户: ${readerInfo.PersonName}');
      }
      
      return true; // 无论成功失败都继续执行其他处理器
      
    } catch (e) {
      debugPrint('自动开门处理异常: $e');
      return true; // 异常情况下也继续执行其他处理器
    }
  }
  
  /// 获取第一个可用的门锁继电器配置
  Future<DoorRelayConfig?> _getFirstDoorLockConfig() async {
    try {
      final configs = DoorRelayConfigManager.getAllConfigs();
      
      if (configs.isEmpty) {
        debugPrint('门锁继电器配置列表为空');
        return null;
      }
      
      // 查找第一个启用的配置
      for (final config in configs) {
        if (config.enabled) {
          debugPrint('找到启用的门锁配置: ${config.name}');
          return config;
        }
      }
      
      // 如果没有启用的配置，使用第一个配置
      debugPrint('没有启用的配置，使用第一个配置: ${configs.first.name}');
      return configs.first;
      
    } catch (e) {
      debugPrint('获取门锁配置失败: $e');
      return null;
    }
  }
  
  /// 执行开门操作
  Future<bool> _performDoorOpen(DoorRelayConfig config, Sip2PatronInfoData readerInfo) async {
    // 从认证页面获取连接
    final manager = AuthPageDoorLockConnections.getConnection(config.comPort);

    if (manager == null) {
      debugPrint('门锁设备连接不可用: ${config.comPort}');
      return false;
    }

    try {
      debugPrint('使用认证页面的门锁连接: ${config.comPort}');
      
      // 3. 获取第一个继电器通道
      final firstChannel = _getFirstRelayChannel(config);
      if (firstChannel == null) {
        debugPrint('未找到可用的继电器通道');
        return false;
      }
      
      debugPrint('使用继电器通道: $firstChannel');
      
      // 4. 打开绿灯（认证成功指示）
      debugPrint('正在打开绿灯...');
      final greenLightOnResult = await manager.controlLED('G', true);
      if (greenLightOnResult.isSuccess) {
        debugPrint('绿灯打开成功');
      } else {
        debugPrint('绿灯打开失败: ${greenLightOnResult.message}');
      }

      // 5. 打开继电器（开门）
      debugPrint('正在开门...');
      final openResult = await manager.controlRelay(firstChannel, true);

      if (!openResult.isSuccess) {
        debugPrint('开门失败: ${openResult.message}');
        // 即使开门失败，也要关闭绿灯
        await manager.controlLED('G', false);
        return false;
      }

      debugPrint('开门成功，等待 $_doorOpenDurationMs 毫秒后关闭');

      // 6. 等待500毫秒后关门
      await Future.delayed(const Duration(milliseconds: _doorOpenDurationMs));

      // 7. 关闭继电器（关门）
      debugPrint('正在关门...');
      final closeResult = await manager.controlRelay(firstChannel, false);

      if (!closeResult.isSuccess) {
        debugPrint('关门失败: ${closeResult.message}，但开门操作已成功');
        // 关门失败不影响开门成功的判断
      } else {
        debugPrint('关门成功');
      }

      // 8. 关闭绿灯
      debugPrint('正在关闭绿灯...');
      final greenLightOffResult = await manager.controlLED('G', false);
      if (greenLightOffResult.isSuccess) {
        debugPrint('绿灯关闭成功');
      } else {
        debugPrint('绿灯关闭失败: ${greenLightOffResult.message}');
      }

      // 9. 记录开门日志
      await _logDoorOperation(readerInfo, config, firstChannel, true);
      
      return true;
      
    } catch (e) {
      debugPrint('开门操作异常: $e');
      return false;
    } finally {
      // 10. 连接由认证页面管理，不在此处断开
      // 连接会在认证页面的dispose中统一清理
      debugPrint('门锁操作完成，连接由认证页面管理');
    }
  }
  
  /// 获取第一个继电器通道
  int? _getFirstRelayChannel(DoorRelayConfig config) {
    try {
      // 检查继电器通道配置
      if (config.relayChannels.isNotEmpty) {
        // relayChannels 是 Map<int, RelayChannelConfig> 类型
        final firstEntry = config.relayChannels.entries.first;
        debugPrint('使用继电器通道: ${firstEntry.key}');
        return firstEntry.key;
      }

      // 如果配置中没有定义通道，默认使用通道1
      debugPrint('配置中未定义继电器通道，使用默认通道1');
      return 1;

    } catch (e) {
      debugPrint('获取继电器通道失败: $e，使用默认通道1');
      return 1; // 出错时也使用默认通道1
    }
  }
  
  /// 记录开门操作日志
  Future<void> _logDoorOperation(
    Sip2PatronInfoData readerInfo, 
    DoorRelayConfig config, 
    int channel, 
    bool success
  ) async {
    try {
      final logMessage = '自动开门操作 - '
          '用户: ${readerInfo.PersonName} (${readerInfo.PatronIdentifier}), '
          '设备: ${config.name}, '
          '通道: $channel, '
          '结果: ${success ? "成功" : "失败"}, '
          '时间: ${DateTime.now().toIso8601String()}';
      
      debugPrint(logMessage);
      
      // 这里可以扩展为写入数据库或日志文件
      // 例如：await DatabaseService.instance.insertDoorLog(...)
      
    } catch (e) {
      debugPrint('记录开门日志失败: $e');
    }
  }
}
