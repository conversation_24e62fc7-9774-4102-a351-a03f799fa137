YuNet ģ�ͼ��ز���ʼ���ɹ���
YuNet model loaded successfully, ready to detect.
Starting face detection thread
=== Starting Baidu Face Recognition SDK Initialization ===
Using full DLL copy deployment, no dynamic path setting needed
Step 1: Creating Baidu<PERSON>aceApi instance...
OK: BaiduFaceApi instance created successfully
Step 2: Getting device information...
Step 2.1: Getting device ID...
OK: Device ID acquired successfully (length: 32)
Step 2.2: Getting SDK version...
Step 2.3: SDK version call completed
OK: SDK Version: 8.4.0
Step 2.4: Device information step completed
Step 2.5: About to proceed to Step 3...
Step 3: Calling sdk_init...
Step 3.1: Using specified model path: D:\FaceOfflineSdk
Step 3.2: About to call g_baidu_api->sdk_init()...
WARNING: Logging before InitGoogleLogging() is written to STDERR
W0729 20:03:21.284134  3088 analysis_predictor.cc:2166] Deprecated. Please use CreatePredictor instead.
Face detection FPS: 3 FPS
Face detection FPS: 25 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Step 3.3: sdk_init call completed
Step 3.4: SDK initialization result code: 0
Step 3.5: SDK initialization successful
Step 3.7: SDK init step completed
Step 3.8: About to proceed to Step 4...
Step 4: Checking authorization status...
Step 4.1: About to call is_auth()...
Step 4.2: is_auth() call completed
Step 4.3: Authorization status: Authorized
Step 4.4: SDK properly authorized
Step 4.5: Authorization check completed
Step 4.6: About to proceed to Step 5...
Step 5: Loading face database...
Step 5.1: About to call load_db_face()...
Step 5.2: load_db_face() call completed
Step 5.3: Face database loading: Success
Step 5.4: About to get face count...
Step 5.5: Face count retrieved successfully
Step 5.6: Current face count in database: 4
Step 5.7: Database loading step completed
Step 5.8: About to proceed to Step 6...
Step 6: Setting initialization flag to true
Step 6.1: Initialization flag set successfully
=== Baidu Face Recognition SDK Initialization Complete ===
Step 6.2: Initialization successful, returning 0
Face database load result: success
Authorization check result: authorized
Face count in database: 4
YuNet model loaded successfully, ready to detect.
Starting face detection thread
has_new_face_capture called: ��������
has_new_face_capture called: ��������
has_new_face_capture called: ��������
Face detection FPS: 1 FPS
has_new_face_capture called: ��������
has_new_face_capture called: ��������
has_new_face_capture called: ��������
Face detection FPS: 24 FPS
has_new_face_capture called: ��������
has_new_face_capture called: ��������
Face detection FPS: 25 FPS
High confidence face detected: 241.936
High confidence face detected: 234.677
High confidence face detected: 225.194
High confidence face detected: 229.349
High confidence face detected: 231.261
has_new_face_capture called: ��������
get_face_capture_info: ���Ŷ�=241.936, ���ݴ�С=2492
========================================
=== Starting 1:N Face Recognition Debug ===
========================================
[Step 1] Parameter Validation:
  - Image data pointer: 0000021501A03640
  - Image data size: 2492 bytes
  - Specified width: 0
  - Specified height: 0
OK: Parameter validation passed

[Step 2] SDK Status Check:
  - SDK initialization status: Initialized
  - API instance status: Exists

[Step 3] Authorization Check:
  - Authorization status: Authorized

[Step 4] Face Database Status:
  - Face count in database: 4

[Step 5] Image Data Analysis:
  - Data format detection: JPEG
  - JPEG header: 0xffd8
  - JPEG integrity: Complete

[Step 6] Converting to OpenCV Mat:
Successfully decoded JPEG: 107x87
OK: Image conversion successful:
  - Converted size: 107x87
  - Channels: 3
  - Data type: 16 (CV_8UC3=16)
  - Data continuity: Continuous

[Step 7] Executing Baidu SDK Face Recognition:
  - Calling identify_with_all interface...
High confidence face detected: 183.544
High confidence face detected: 153.388
  - Recognition time: 244 milliseconds
  - Result length: 263 characters

[Step 8] Recognition Result Analysis:
OK: Recognition result obtained:
  - Result content: {
        "data" :
        {
                "face_token" : "89551768e2ba157785f0c5fd0a697f1a",
                "log_id" : "1753790613854",
                "result" :
                [
                        {
                                "group_id" : "hh_group",
                                "score" : 23.32,
                                "user_id" : "hh065"
                        }
                ],
                "result_num" : 1
        },
        "errno" : 0,
        "msg" : "success"
}
  - Result type: Custom format

========================================
=== 1:N Face Recognition Debug Complete ===
========================================
High confidence face detected: 174.687
High confidence face detected: 181.063
High confidence face detected: 174.161
High confidence face detected: 193.297
High confidence face detected: 182.133
Face detection FPS: 19 FPS
High confidence face detected: 181.356
High confidence face detected: 173.398
High confidence face detected: 176.45
High confidence face detected: 172.044
High confidence face detected: 169.116
High confidence face detected: 167.792
High confidence face detected: 168.38
High confidence face detected: 167.376
High confidence face detected: 168.314
High confidence face detected: 165.77
High confidence face detected: 162.397
High confidence face detected: 162.403
High confidence face detected: 163.049
High confidence face detected: 164.586
High confidence face detected: 164.477
High confidence face detected: 158.314
Face detection FPS: 16 FPS
High confidence face detected: 156.73
High confidence face detected: 156.022
High confidence face detected: 156.766
High confidence face detected: 158.443
High confidence face detected: 159.558
High confidence face detected: 159.4
High confidence face detected: 159.703
High confidence face detected: 159.094
High confidence face detected: 159.016
High confidence face detected: 157.864
has_new_face_capture called: ��������
get_face_capture_info: ���Ŷ�=156.022, ���ݴ�С=5267
========================================
=== Starting 1:N Face Recognition Debug ===
========================================
[Step 1] Parameter Validation:
  - Image data pointer: 000002150A5263A0
  - Image data size: 5267 bytes
  - Specified width: 0
  - Specified height: 0
OK: Parameter validation passed

[Step 2] SDK Status Check:
  - SDK initialization status: Initialized
  - API instance status: Exists

[Step 3] Authorization Check:
  - Authorization status: Authorized

[Step 4] Face Database Status:
  - Face count in database: 4

[Step 5] Image Data Analysis:
  - Data format detection: JPEG
  - JPEG header: 0xffd8
  - JPEG integrity: Complete

[Step 6] Converting to OpenCV Mat:
Successfully decoded JPEG: 104x140
OK: Image conversion successful:
  - Converted size: 104x140
  - Channels: 3
  - Data type: 16 (CV_8UC3=16)
  - Data continuity: Continuous

[Step 7] Executing Baidu SDK Face Recognition:
  - Calling identify_with_all interface...
High confidence face detected: 156.434
High confidence face detected: 156.69
  - Recognition time: 129 milliseconds
  - Result length: 264 characters

[Step 8] Recognition Result Analysis:
OK: Recognition result obtained:
  - Result content: {
        "data" :
        {
                "face_token" : "a6085d1a4f971a4508738b49e4030920",
                "log_id" : "1753790615988",
                "result" :
                [
                        {
                                "group_id" : "hh_group",
                                "score" : 92.94,
                                "user_id" : "111111"
                        }
                ],
                "result_num" : 1
        },
        "errno" : 0,
        "msg" : "success"
}
  - Result type: Custom format

========================================
=== 1:N Face Recognition Debug Complete ===
========================================
High confidence face detected: 156.449
High confidence face detected: 155.126
High confidence face detected: 154.487
High confidence face detected: 155.569
High confidence face detected: 157.513
Face detection FPS: 17 FPS
High confidence face detected: 157.133
High confidence face detected: 157.467
High confidence face detected: 158.689
High confidence face detected: 159.265
High confidence face detected: 160.85
High confidence face detected: 161.416
High confidence face detected: 159.658
High confidence face detected: 160.62
High confidence face detected: 161.641
High confidence face detected: 162.736
High confidence face detected: 163.628
High confidence face detected: 167.615
High confidence face detected: 173.266
High confidence face detected: 183.994
High confidence face detected: 185.277
High confidence face detected: 188.51
High confidence face detected: 197.621
Face detection FPS: 17 FPS
Face detection FPS: 19 FPS
Face detection FPS: 20 FPS
has_new_face_capture called: ��������
get_face_capture_info: ���Ŷ�=157.467, ���ݴ�С=5600
========================================
=== Starting 1:N Face Recognition Debug ===
========================================
[Step 1] Parameter Validation:
  - Image data pointer: 0000021501A02020
  - Image data size: 5600 bytes
  - Specified width: 0
  - Specified height: 0
OK: Parameter validation passed

[Step 2] SDK Status Check:
  - SDK initialization status: Initialized
  - API instance status: Exists

[Step 3] Authorization Check:
  - Authorization status: Authorized

[Step 4] Face Database Status:
  - Face count in database: 4

[Step 5] Image Data Analysis:
  - Data format detection: JPEG
  - JPEG header: 0xffd8
  - JPEG integrity: Complete

[Step 6] Converting to OpenCV Mat:
Successfully decoded JPEG: 102x137
OK: Image conversion successful:
  - Converted size: 102x137
  - Channels: 3
  - Data type: 16 (CV_8UC3=16)
  - Data continuity: Continuous

[Step 7] Executing Baidu SDK Face Recognition:
  - Calling identify_with_all interface...
  - Recognition time: 128 milliseconds
  - Result length: 264 characters

[Step 8] Recognition Result Analysis:
OK: Recognition result obtained:
  - Result content: {
        "data" :
        {
                "face_token" : "5fd01111e2d774d9f1edd85350d0a557",
                "log_id" : "1753790620123",
                "result" :
                [
                        {
                                "group_id" : "hh_group",
                                "score" : 94.02,
                                "user_id" : "111111"
                        }
                ],
                "result_num" : 1
        },
        "errno" : 0,
        "msg" : "success"
}
  - Result type: Custom format

========================================
=== 1:N Face Recognition Debug Complete ===
========================================
Face detection FPS: 20 FPS
Face detection FPS: 20 FPS
Face detection FPS: 21 FPS
Face detection FPS: 20 FPS
has_new_face_capture called: ��������
Face detection FPS: 25 FPS
Face detection FPS: 26 FPS
has_new_face_capture called: ��������
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 25 FPS
Face detection FPS: 25 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
Face detection FPS: 26 FPS
YuNet model loaded successfully, ready to detect.
Starting face detection thread
[ WARN:0@39.328] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.348] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.348] global cap_msmf.cpp:551 `anonymous-namespace'::SourceReaderCB::~SourceReaderCB terminating async callback
[ INFO:0@39.373] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.374] global cap_msmf.cpp:551 `anonymous-namespace'::SourceReaderCB::~SourceReaderCB terminating async callback
[ INFO:0@39.400] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.400] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.401] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.418] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.442] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.449] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.449] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.451] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.469] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.503] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.515] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.515] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.516] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.541] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.589] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.596] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.596] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.598] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.615] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.655] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.671] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.671] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.672] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.708] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.736] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.742] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.743] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.744] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.773] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.799] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.807] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.807] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.808] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.826] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.856] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.865] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.865] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
[ WARN:0@39.867] global cap.cpp:480 cv::VideoCapture::open VIDEOIO(DSHOW): backend is generally available but can't be used to capture by index
[ INFO:0@39.889] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.914] global cap_msmf.cpp:1033 CvCapture_MSMF::configureHW MSMF: Using D3D11 video acceleration on GPU device: NVIDIA GeForce GT 705
[ INFO:0@39.922] global obsensor_stream_channel_msmf.cpp:200 cv::obsensor::MFContext::queryUvcDeviceInfoList UVC device found: name=PC Camera, vid=1423, pid=866, mi=0, uid=54bc70f, guid={e5323777-f976-4f5b-9b55-b94699c46e44}\global
[ERROR:0@39.922] global obsensor_uvc_stream_channel.cpp:158 cv::obsensor::getStreamChannelGroup Camera index out of range
YuNet model loaded successfully, ready to detect.
Starting face detection thread
=== Setting SDK Path (Compatibility Mode) ===
SDK path recorded: D:\FaceOfflineSdk
Note: Using full DLL copy deployment, no dynamic path setting needed
Authorization check result: authorized
Face count in database: 4
has_new_face_capture called: ��������
has_new_face_capture called: ��������
Face detection FPS: 1 FPS
has_new_face_capture called: ��������
has_new_face_capture called: ��������
Face detection FPS: 24 FPS
has_new_face_capture called: ��������
High confidence face detected: 167.419
High confidence face detected: 169.487
High confidence face detected: 165.936
High confidence face detected: 157.922
High confidence face detected: 165.78
High confidence face detected: 161.62
High confidence face detected: 158.504
High confidence face detected: 157.415
High confidence face detected: 154.117
has_new_face_capture called: ��������
get_face_capture_info: ���Ŷ�=167.419, ���ݴ�С=2431
========================================
=== Starting 1:N Face Recognition Debug ===
========================================
[Step 1] Parameter Validation:
  - Image data pointer: 0000021549A45430
  - Image data size: 2431 bytes
  - Specified width: 0
  - Specified height: 0
OK: Parameter validation passed

[Step 2] SDK Status Check:
  - SDK initialization status: Initialized
  - API instance status: Exists

[Step 3] Authorization Check:
  - Authorization status: Authorized

[Step 4] Face Database Status:
  - Face count in database: 4

[Step 5] Image Data Analysis:
  - Data format detection: JPEG
  - JPEG header: 0xffd8
  - JPEG integrity: Complete

[Step 6] Converting to OpenCV Mat:
Successfully decoded JPEG: 87x101
OK: Image conversion successful:
  - Converted size: 87x101
  - Channels: 3
  - Data type: 16 (CV_8UC3=16)
  - Data continuity: Continuous

[Step 7] Executing Baidu SDK Face Recognition:
  - Calling identify_with_all interface...
High confidence face detected: 152.795
Face detection FPS: 20 FPS
High confidence face detected: 154.184
  - Recognition time: 130 milliseconds
  - Result length: 264 characters

[Step 8] Recognition Result Analysis:
OK: Recognition result obtained:
  - Result content: {
        "data" :
        {
                "face_token" : "855356c49b578fdf67450c61d21f445a",
                "log_id" : "1753790643200",
                "result" :
                [
                        {
                                "group_id" : "hh_group",
                                "score" : 22.11,
                                "user_id" : "111111"
                        }
                ],
                "result_num" : 1
        },
        "errno" : 0,
        "msg" : "success"
}
  - Result type: Custom format

========================================
=== 1:N Face Recognition Debug Complete ===
========================================
High confidence face detected: 155.162
High confidence face detected: 156.421
High confidence face detected: 152.515
High confidence face detected: 153.425
High confidence face detected: 152.487
High confidence face detected: 151.975
High confidence face detected: 150.991
High confidence face detected: 150.852
High confidence face detected: 150.048
High confidence face detected: 148.784
High confidence face detected: 149.612
High confidence face detected: 148.241
High confidence face detected: 148.819
High confidence face detected: 150.083
High confidence face detected: 150.733
High confidence face detected: 149.971
Face detection FPS: 17 FPS
High confidence face detected: 149.488
High confidence face detected: 149.472
High confidence face detected: 149.755
High confidence face detected: 149.813
High confidence face detected: 149.242
High confidence face detected: 149.365
High confidence face detected: 149.633
High confidence face detected: 149.223
High confidence face detected: 150.494
High confidence face detected: 149.551
High confidence face detected: 149.994
High confidence face detected: 149.628
High confidence face detected: 149.401
High confidence face detected: 148.631
High confidence face detected: 149.005
High confidence face detected: 149.44
High confidence face detected: 149.666
Face detection FPS: 17 FPS
has_new_face_capture called: ��������
get_face_capture_info: ���Ŷ�=149.223, ���ݴ�С=5087
========================================
=== Starting 1:N Face Recognition Debug ===
========================================
[Step 1] Parameter Validation:
  - Image data pointer: 000002151D27B670
  - Image data size: 5087 bytes
  - Specified width: 0
  - Specified height: 0
OK: Parameter validation passed

[Step 2] SDK Status Check:
  - SDK initialization status: Initialized
  - API instance status: Exists

[Step 3] Authorization Check:
  - Authorization status: Authorized

[Step 4] Face Database Status:
  - Face count in database: 4

[Step 5] Image Data Analysis:
  - Data format detection: JPEG
  - JPEG header: 0xffd8
  - JPEG integrity: Complete

[Step 6] Converting to OpenCV Mat:
Successfully decoded JPEG: 100x123
OK: Image conversion successful:
  - Converted size: 100x123
  - Channels: 3
  - Data type: 16 (CV_8UC3=16)
  - Data continuity: Continuous

[Step 7] Executing Baidu SDK Face Recognition:
  - Calling identify_with_all interface...
High confidence face detected: 148.512
High confidence face detected: 149.633
  - Recognition time: 121 milliseconds
  - Result length: 263 characters

[Step 8] Recognition Result Analysis:
OK: Recognition result obtained:
  - Result content: {
        "data" :
        {
                "face_token" : "25aaf945d9a0476bbe7791048a9a7676",
                "log_id" : "1753790645326",
                "result" :
                [
                        {
                                "group_id" : "hh_group",
                                "score" : 90.6,
                                "user_id" : "111111"
                        }
                ],
                "result_num" : 1
        },
        "errno" : 0,
        "msg" : "success"
}
  - Result type: Custom format

========================================
=== 1:N Face Recognition Debug Complete ===
========================================
High confidence face detected: 149.594
High confidence face detected: 151.724
High confidence face detected: 151.492
High confidence face detected: 151.513
High confidence face detected: 151.998
High confidence face detected: 151
High confidence face detected: 151.114
High confidence face detected: 150.67
High confidence face detected: 150.994
High confidence face detected: 151.194
High confidence face detected: 150.681
High confidence face detected: 147.95
High confidence face detected: 154.704
High confidence face detected: 159.553
High confidence face detected: 159.273
Face detection FPS: 17 FPS
High confidence face detected: 149.478
High confidence face detected: 146.663
High confidence face detected: 145.799
High confidence face detected: 143.542
High confidence face detected: 145.103
High confidence face detected: 144.275
High confidence face detected: 141.56
High confidence face detected: 140.923
High confidence face detected: 137.771
High confidence face detected: 134.682
High confidence face detected: 134.066
High confidence face detected: 129.456
High confidence face detected: 130.252
High confidence face detected: 133.052
High confidence face detected: 139.947
High confidence face detected: 142.056
High confidence face detected: 147.03
Face detection FPS: 17 FPS
High confidence face detected: 148.972
High confidence face detected: 148.714
High confidence face detected: 148.644
High confidence face detected: 147.413
High confidence face detected: 150.191
High confidence face detected: 153.85
High confidence face detected: 153.869
High confidence face detected: 152.004
High confidence face detected: 152.404
High confidence face detected: 149.616
High confidence face detected: 150.354
High confidence face detected: 149.158
High confidence face detected: 140.842
High confidence face detected: 131.291
High confidence face detected: 137.38
High confidence face detected: 135.308
High confidence face detected: 125.997
Face detection FPS: 17 FPS
High confidence face detected: 123.678
High confidence face detected: 126.155
High confidence face detected: 124.719
High confidence face detected: 125.248
High confidence face detected: 120.406
High confidence face detected: 118.519
High confidence face detected: 116.906
High confidence face detected: 117.313
High confidence face detected: 116.134
High confidence face detected: 115.083
High confidence face detected: 117.897
High confidence face detected: 121.306
High confidence face detected: 116.448
High confidence face detected: 161.366
High confidence face detected: 161.821
High confidence face detected: 153.244
Face detection FPS: 17 FPS
High confidence face detected: 138.806
High confidence face detected: 166.249
High confidence face detected: 164.175
High confidence face detected: 142.544
High confidence face detected: 166.866
High confidence face detected: 159.982
High confidence face detected: 149.484
High confidence face detected: 139.437
High confidence face detected: 136.31
High confidence face detected: 131.973
High confidence face detected: 137.123
High confidence face detected: 137.146
High confidence face detected: 137.862
High confidence face detected: 139.448
High confidence face detected: 137.912
High confidence face detected: 134.627
High confidence face detected: 140.428
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 137.862
High confidence face detected: 139.448
High confidence face detected: 137.912
High confidence face detected: 134.627
High confidence face detected: 140.428
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 139.448
High confidence face detected: 137.912
High confidence face detected: 134.627
High confidence face detected: 140.428
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 134.627
High confidence face detected: 140.428
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 140.428
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
Face detection FPS: 17 FPS
High confidence face detected: 137.781
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 135.938
High confidence face detected: 134.999
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 130.006
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 131.513
High confidence face detected: 125.363
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 120.305
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 117.143
High confidence face detected: 114.504
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 130.301
High confidence face detected: 115.846
High confidence face detected: 122.937
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 130.301
High confidence face detected: 131.517
High confidence face detected: 131.732
High confidence face detected: 133.243
High confidence face detected: 124.547
High confidence face detected: 130.301
High confidence face detected: 131.517
High confidence face detected: 131.732
High confidence face detected: 130.301
High confidence face detected: 131.517
High confidence face detected: 131.732
High confidence face detected: 131.517
High confidence face detected: 131.732
High confidence face detected: 129.382
High confidence face detected: 129.382
Face detection FPS: 17 FPS
High confidence face detected: 148.799
High confidence face detected: 161.202
High confidence face detected: 198.566
High confidence face detected: 246.312
High confidence face detected: 224.758
High confidence face detected: 236.728
High confidence face detected: 172.049
High confidence face detected: 212.604
Face detection FPS: 17 FPS
Face detection FPS: 18 FPS
Face detection FPS: 25 FPS
Face detection FPS: 24 FPS
Face detection FPS: 27 FPS