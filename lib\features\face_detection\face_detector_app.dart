import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:ui' as ui;
import 'dart:ffi' as ffi;
import 'face_detector.dart';
import 'face_capture_polling.dart';
import 'dart:math' as math;
import 'package:file_picker/file_picker.dart';
import 'event_channel_demo.dart';
import 'face_recognition_service.dart';
import 'baidu_face_recognition.dart';
import 'sdk_config_ui.dart';
import 'sdk_config_manager.dart';
import 'package:path/path.dart' as path;

// void main() async {
//   WidgetsFlutterBinding.ensureInitialized();
  
//   // 初始化人脸检测器
//   await FaceDetector.initialize();
//   FaceDetector.setUseHaarDetector(false);
//   print("主程序入口强制使用DNN检测器");
  
//   // 初始化百度人脸识别SDK（使用配置管理器）
//   try {
//     final configManager = SdkConfigManager.instance;
//     final sdkPath = await configManager.getSdkPath();
//     print("从配置获取SDK路径: $sdkPath");
    
//     // 验证SDK路径
//     final validation = await configManager.validateSdkPath(sdkPath);
//     if (!validation.isValid) {
//       print("⚠️ SDK路径验证失败: ${validation.error}");
//       print("请在应用启动后使用SDK配置界面设置正确的路径");
//     } else {
//       print("✅ SDK路径验证成功");
      
//       // 设置SDK路径配置（兼容性）
//       print("🔧 配置SDK路径: $sdkPath");
//       final setResult = BaiduFaceRecognition.setSdkPath(sdkPath);
//       print("✅ SDK路径配置完成，错误码: $setResult");
      
//       // 初始化SDK（使用全量DLL复制方案）
//       print("🚀 开始初始化百度人脸识别SDK（全量DLL复制模式）...");
//       final baiduInitialized = await BaiduFaceRecognition.initialize(modelPath: sdkPath);
//       if (baiduInitialized) {
//         print("✅ 百度人脸识别SDK初始化成功");
//       } else {
//         print("⚠️ 百度人脸识别SDK初始化失败，将使用OpenCV备用方案");
//       }
//     }
//   } catch (e) {
//     print("⚠️ 百度人脸识别SDK初始化异常: $e，将使用OpenCV备用方案");
//   }
  
//   // 初始化人脸识别服务
//   final faceRecognitionService = FaceRecognitionService();
//   await faceRecognitionService.initialize();
  
//   print("=== 所有初始化完成，等待5秒后启动UI ===");
//   await Future.delayed(Duration(seconds: 5));
//   print("=== 开始启动Flutter UI ===");
  
//   runApp(MaterialApp(home: FaceDetectorApp()));
// }

class FaceDetectorApp extends StatefulWidget {
  const FaceDetectorApp({super.key});



  @override
  _FaceDetectorAppState createState() => _FaceDetectorAppState();


  
}

class _FaceDetectorAppState extends State<FaceDetectorApp>
    with WidgetsBindingObserver {
  bool _isProcessing = false;
  bool _isDetecting = false; // 是否正在执行人脸检测
  bool _isRecognizing = false; // 是否正在执行百度人脸识别
  Uint8List? _jpegImageData;
  List<Map<dynamic, dynamic>> _faces = [];
  bool _initialized = false;
  Timer? _frameTimer;
  Timer? _detectionTimer; // 单独的人脸检测定时器
  int _jpegQuality = 80;
  int _processingTime = 0;
  int _detectionTime = 0; // 人脸检测耗时
  int _frameInterval = 80; // 修改默认为80毫秒捕获一帧（12.5fps）
  int _detectionInterval = 80; // 修改默认为80毫秒检测一次人脸，提高实时性
  int _skipFrameCount = 0; // 用于跟踪跳过帧的数量
  bool _isFaceDetectionEnabled = false; // 人脸检测开关默认关闭
  bool _isHighFPSEnabled = true; // 高帧率模式默认开启
  bool _useHaarDetector = false; // 默认使用DNN而非Haar级联分类器

  // 添加实际帧率计算
  int _actualFPS = 0;
  int _frameCount = 0;
  DateTime _lastFPSUpdate = DateTime.now();

  double _smoothingFactor = 0.7; // 平滑因子
  bool _adaptiveQualityEnabled = true; // 是否启用自适应质量控制
  int _cpuLoadThreshold = 70; // CPU负载阈值
  bool _isPerformanceWarning = false; // 性能警告标志
  int _adaptiveQualityInterval = 2000; // 自适应质量更新间隔
  Timer? _adaptiveQualityTimer; // 自适应质量定时器

  // 轮询机制相关变量
  FaceCapturePolling? _faceCapturePolling;
  bool _isPollingEnabled = false;
  Uint8List? _latestCapturedFace;
  double _latestFaceConfidence = 0.0;
  
  // 人脸识别服务
  late final FaceRecognitionService _faceRecognitionService;
  StreamSubscription? _recognitionSubscription;
  FaceRecognitionResult? _lastRecognitionResult;

  // 百度人脸识别相关变量
  bool _baiduSdkInitialized = false;
  int _baiduFaceCount = 0;
  List<FaceRecognitionResult> _baiduRecognitionResults = [];
  String _baiduStatusMessage = '未初始化';
  bool _baiduRecognitionEnabled = false;



  int _loadUpdateCounter = 0;  // 用于减少负载UI更新频率
  int _uiUpdateInterval = 5;   // 每5帧更新一次UI
  int _systemLoad = 0;         // 系统负载(0-100)
  int _adaptiveFrameThreshold = 60; // 负载阈值，超过此值降低帧率

  // 添加新变量用于结果平滑处理
  List<Map<dynamic, dynamic>> _lastDetectedFaces = [];
  int _noFaceFrameCount = 0;
  final int _maxNoFaceFrames = 5; // 允许最多5帧无人脸结果后才清除显示
  
  // 添加人脸位置预测变量
  List<Map<dynamic, dynamic>> _predictedFaces = [];
  List<Map<dynamic, dynamic>> _previousFaces = [];
  DateTime _lastDetectionTime = DateTime.now();
  double _faceMovementWeight = 0.7; // 移动预测权重，值越高响应越快
  bool _enablePrediction = true; // 启用位置预测
  
  // 添加拍照和录像相关变量
  bool _isRecording = false; // 是否正在录像
  String? _recordingPath; // 当前录像保存路径
  String? _lastPhotoPath; // 上一次拍照的保存路径
  String? _customSavePath; // 用户自定义保存路径
  
  // 摄像头选择相关变量
  int _currentCameraId = 0; // 当前摄像头ID，默认使用摄像头0
  bool _isCameraChanging = false; // 是否正在切换摄像头

  // 添加显示人脸框的开关
  bool _showFaceBoxes = true;

  // 添加摄像头开启状态变量
  bool _isCameraRunning = false;

  // 移除性能监控变量
  int _baseFrameInterval = 80; // 基础帧间隔，修改为80毫秒（12.5fps）
  int _highFPSInterval = 80; // 高帧率模式下的帧间隔修改为80ms (12.5fps)
  final GlobalKey _imageKey = GlobalKey(); // 使用固定的GlobalKey，避免重新创建

  // 添加照片和视频分别的保存路径变量
  String? _photoSavePath; // 照片保存路径
  String? _videoSavePath; // 视频保存路径

  // EventChannel 相关变量
  static const EventChannel _eventChannel = EventChannel('face_detection_events');
  static const MethodChannel _methodChannel = MethodChannel('face_detection');
  StreamSubscription? _streamSubscription;
  bool _isEventChannelActive = false;
  Uint8List? _detectedFaceImage; // 检测到的高置信度人脸图片
  double _lastFaceConfidence = 0.0; // 最后检测到的人脸置信度

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    
      pp();

    _initDetector();
    _initFaceCapturePolling();
    _initFaceRecognitionManager();
    
    // 检查百度人脸识别SDK状态
    _checkBaiduSdkStatus();

    // 每秒更新实际帧率
    Timer.periodic(const Duration(seconds: 1), (_) {
      _updateActualFPS();
      // 同时获取系统负载
      _systemLoad = FaceDetector.getSystemLoad();
    });
    
    // 启动自适应质量控制
    _startAdaptiveQualityControl();

    // 设置初始处理间隔
    FaceDetector.setProcessingInterval(30);
    
    // 定时更新系统负载
    Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (_initialized) {
        setState(() {
          _systemLoad = FaceDetector.getSystemLoad();
          
          // 自适应质量控制逻辑
          if (_adaptiveQualityEnabled) {
            // 根据系统负载动态调整JPEG质量
            if (_systemLoad > 80) {
              // 高负载时降低质量和检测频率
              FaceDetector.setJpegQuality(50);
              FaceDetector.setProcessingInterval(80);
            } else if (_systemLoad > 60) {
              FaceDetector.setJpegQuality(65);
              FaceDetector.setProcessingInterval(50);
            } else if (_systemLoad > 40) {
              FaceDetector.setJpegQuality(75);
              FaceDetector.setProcessingInterval(40);
            } else {
              // 低负载时提高质量
              FaceDetector.setJpegQuality(85);
              FaceDetector.setProcessingInterval(30);
            }
          }
        });
      }
    });
    
    // 初始化EventChannel
    _initEventChannel();
  }

  void pp () async{

      // 初始化人脸检测器（懒加载 - 只在未初始化时执行）
  print("📷 检查人脸检测器初始化状态...");
  final faceDetectorStartTime = DateTime.now();

  if (!FaceDetector.isInitialized) {
    print("🔧 开始初始化人脸检测器...");
    await FaceDetector.initialize();
    final faceDetectorEndTime = DateTime.now();
    final faceDetectorDuration = faceDetectorEndTime.difference(faceDetectorStartTime).inMilliseconds;
    print("✅ 人脸检测器初始化完成，耗时: ${faceDetectorDuration}ms");
  } else {
    print("✅ 人脸检测器已初始化，跳过初始化步骤");
  }

  FaceDetector.setUseHaarDetector(false);
  print("主程序入口强制使用DNN检测器");
  
  // 初始化百度人脸识别SDK（使用配置管理器）
  try {
    final configManager = SdkConfigManager.instance;
    final sdkPath = await configManager.getSdkPath();
    print("从配置获取SDK路径: $sdkPath");
    
    // 验证SDK路径
    final validation = await configManager.validateSdkPath(sdkPath);
    if (!validation.isValid) {
      print("⚠️ SDK路径验证失败: ${validation.error}");
      print("请在应用启动后使用SDK配置界面设置正确的路径");
    } else {
      print("✅ SDK路径验证成功");
      
      // 设置SDK路径配置（兼容性）
      print("🔧 配置SDK路径: $sdkPath");
      final setResult = BaiduFaceRecognition.setSdkPath(sdkPath);
      print("✅ SDK路径配置完成，错误码: $setResult");
      
      // 初始化SDK（使用全量DLL复制方案）
      print("🚀 开始初始化百度人脸识别SDK（全量DLL复制模式）...");
      final baiduInitialized = await BaiduFaceRecognition.initialize(modelPath: sdkPath);
      if (baiduInitialized) {
        print("✅ 百度人脸识别SDK初始化成功");
      } else {
        print("⚠️ 百度人脸识别SDK初始化失败，将使用OpenCV备用方案");
      }
    }
  } catch (e) {
    print("⚠️ 百度人脸识别SDK初始化异常: $e，将使用OpenCV备用方案");
  }

  
  // 初始化人脸识别服务
  final faceRecognitionService = FaceRecognitionService();
  await faceRecognitionService.initialize();

  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopFrameCapture();
    _stopDetection();
    _stopAdaptiveQualityControl();
    _streamSubscription?.cancel();
    _faceCapturePolling?.dispose(); // 清理轮询服务
    FaceDetector.stopCamera();
    
    // 清理人脸识别服务订阅
    _recognitionSubscription?.cancel();
    _faceRecognitionService.dispose();
    
    super.dispose();
  }
  
  // 初始化人脸识别管理器
  void _initFaceRecognitionManager() {
    _faceRecognitionService = FaceRecognitionService();
    
    // 订阅识别结果
    _recognitionSubscription = _faceRecognitionService.recognitionStream.listen((event) {
      switch (event.type) {
        case RecognitionEventType.matchFound:
          setState(() {
            _lastRecognitionResult = event.recognitionResult;
            _latestCapturedFace = event.faceImage;
            _latestFaceConfidence = event.confidence ?? 0.0;
          });
          
          // 显示识别结果弹窗
          _showRecognitionResultDialog(event.recognitionResult!);
          break;
          
        case RecognitionEventType.faceCaptured:
          setState(() {
            _latestCapturedFace = event.faceImage;
            _latestFaceConfidence = event.confidence ?? 0.0;
          });
          break;
          
        case RecognitionEventType.error:
          // 显示错误提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('错误: ${event.message}'),
              backgroundColor: Colors.red,
            ),
          );
          break;
          
        default:
          // 其他事件处理
          break;
      }
    });
  }
  
  // 执行百度人脸识别（用于轮询自动识别）
  Future<void> _performBaiduRecognition(Uint8List imageData) async {
    // 【并发控制】如果正在识别，跳过当前帧
    if (_isRecognizing) {
      print('⏭️ 百度识别进行中，跳过当前帧');
      return;
    }
    
    _isRecognizing = true;
    print('🔄 开始百度人脸识别...');
    
    try {
      setState(() {
        _baiduStatusMessage = '自动识别中...';
      });
      
      final results = await BaiduFaceRecognition.identifyWithImageData(
        imageData,
        isJpeg: true
      );
      
      setState(() {
        _baiduRecognitionResults = results;
        if (results.isEmpty) {
          _baiduStatusMessage = '未找到匹配的人脸';
        } else {
          _baiduStatusMessage = '识别成功！匹配到: ${results.first.userId}';
          print('🎉 百度SDK识别成功: ${results.first.userId} (得分: ${results.first.score.toStringAsFixed(1)}%)');
          
          // 如果得分足够高，显示识别结果（百度SDK分数范围是0-100）
          if (results.first.score > 70.0) {
            // _showBaiduRecognitionResult(results.first);
          }
        }
      });
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      setState(() {
        _baiduStatusMessage = errorMessage;
        _baiduRecognitionResults = [];
      });
      
      // 显示错误提示给用户
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: '关闭',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      // 【并发控制】确保状态始终重置
      _isRecognizing = false;
      print('✅ 百度人脸识别完成，释放锁');
    }
  }
  
  // 检查百度人脸识别SDK状态
  void _checkBaiduSdkStatus() {
    Timer(const Duration(seconds: 1), () async {
      try {
        print('=== 开始百度SDK状态检查 ===');
        
        // 检查SDK是否真的已初始化
        bool isInitialized = BaiduFaceRecognition.isInitialized();
        print('当前初始化状态: $isInitialized');
        
        // 如果未初始化，尝试重新初始化
        if (!isInitialized) {
          print('🔄 百度SDK未初始化，尝试重新初始化...');
          const sdkPath = "";
          isInitialized = await BaiduFaceRecognition.initialize(modelPath: sdkPath);
          if (isInitialized) {
            print('✅ 百度SDK重新初始化成功');
          } else {
            print('❌ 百度SDK重新初始化失败');
          }
        }
        
        // 获取详细状态信息
        if (isInitialized) {
          final status = BaiduFaceRecognition.getSdkStatus();
          print('SDK详细状态: $status');
          
          // 检查授权状态
          bool isAuthorized = BaiduFaceRecognition.isAuthorized();
          print('授权状态: ${isAuthorized ? "已授权" : "未授权"}');
          
          // 获取人脸数量
          int faceCount = BaiduFaceRecognition.getFaceCount();
          print('数据库中人脸数量: $faceCount');
        }
        
        setState(() {
          _baiduSdkInitialized = isInitialized;
          if (isInitialized) {
            _baiduFaceCount = BaiduFaceRecognition.getFaceCount();
            bool isAuthorized = BaiduFaceRecognition.isAuthorized();
            _baiduStatusMessage = '人脸库中共有 $_baiduFaceCount 个人脸 ${isAuthorized ? "(已授权)" : "(未授权)"}';
            print('✅ 百度SDK状态检查: 已初始化，人脸数量: $_baiduFaceCount，授权状态: ${isAuthorized ? "已授权" : "未授权"}');
          } else {
            _baiduStatusMessage = '百度SDK未初始化';
            print('❌ 百度SDK状态检查: 未初始化');
          }
        });
        
        print('=== 百度SDK状态检查完成 ===');
      } catch (e) {
        print('百度SDK状态检查异常: $e');
        setState(() {
          _baiduSdkInitialized = false;
          _baiduStatusMessage = 'SDK状态检查失败: $e';
        });
      }
    });
  }
  
  // 使用百度SDK进行人脸识别
  Future<void> _recognizeWithBaiduSdk() async {
    if (!_baiduSdkInitialized) {
      setState(() {
        _baiduStatusMessage = '百度SDK未初始化';
      });
      return;
    }
    
    // 【并发控制】如果正在识别，提示用户稍等
    if (_isRecognizing) {
      setState(() {
        _baiduStatusMessage = '识别正在进行中，请稍等...';
      });
      print('⏭️ 百度识别进行中，手动识别请求被跳过');
      return;
    }
    
    if (_jpegImageData == null) {
      setState(() {
        _baiduStatusMessage = '没有图像数据';
      });
      return;
    }
    
    _isRecognizing = true;
    print('🔄 开始百度人脸识别（手动触发）...');
    
    setState(() {
      _baiduStatusMessage = '正在使用百度SDK识别人脸...';
    });
    
    try {
      final results = await BaiduFaceRecognition.identifyWithImageData(
        _jpegImageData!,
        isJpeg: true
      );
      
      setState(() {
        _baiduRecognitionResults = results;
        if (results.isEmpty) {
          _baiduStatusMessage = '未识别到匹配的人脸';
        } else {
          _baiduStatusMessage = '识别到 ${results.length} 个匹配的人脸';
          // 显示第一个匹配结果
          if (results.isNotEmpty) {
            // _showBaiduRecognitionResult(results.first);
          }
        }
      });
    } catch (e) {
      final errorMessage = e.toString().replaceFirst('Exception: ', '');
      setState(() {
        _baiduStatusMessage = errorMessage;
        _baiduRecognitionResults = [];
      });
      print('❌ 百度SDK手动识别失败: $errorMessage');
      
      // 显示错误提示给用户
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage,
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red.shade600,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: '关闭',
              textColor: Colors.white,
              onPressed: () {
                ScaffoldMessenger.of(context).hideCurrentSnackBar();
              },
            ),
          ),
        );
      }
    } finally {
      // 【并发控制】确保状态始终重置
      _isRecognizing = false;
      print('✅ 百度人脸识别完成（手动触发），释放锁');
    }
  }
  
  // 显示百度人脸识别结果
  void _showBaiduRecognitionResult(FaceRecognitionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: const [
            Icon(Icons.face, color: Colors.blue),
            SizedBox(width: 8),
            Text('百度人脸识别结果'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('用户ID: ${result.userId}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text('分组ID: ${result.groupId}'),
            const SizedBox(height: 4),
            Text(
              '匹配得分: ${result.score.toStringAsFixed(2)}%', 
              style: TextStyle(
                color: result.score > 80 ? Colors.green : (result.score > 60 ? Colors.orange : Colors.red),
                fontWeight: FontWeight.bold
              )
            ),
            if (result.userInfo != null) ...[
              const SizedBox(height: 8),
              const Text('用户信息:', style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(result.userInfo!),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
  
  // 显示人脸识别结果弹窗
  void _showRecognitionResultDialog(FaceRecognitionResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: const [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('人脸识别成功'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('用户ID: ${result.userId}', style: const TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text('分组ID: ${result.groupId}'),
            const SizedBox(height: 4),
            Text(
              '匹配得分: ${result.score.toStringAsFixed(2)}%', 
              style: TextStyle(
                color: result.score > 80 ? Colors.green : (result.score > 60 ? Colors.orange : Colors.red),
                fontWeight: FontWeight.bold
              )
            ),
            if (result.userInfo != null) ...[
              const SizedBox(height: 8),
              const Text('用户信息:', style: TextStyle(fontWeight: FontWeight.bold)),
              Container(
                margin: const EdgeInsets.only(top: 4),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(result.userInfo!),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  // 初始化EventChannel
  void _initEventChannel() {
    try {
      // 监听人脸检测事件流
      _streamSubscription = _eventChannel
          .receiveBroadcastStream()
          .listen(
            (dynamic event) {
              print('收到人脸检测事件: $event');
              if (event is Map) {
                // 处理人脸检测结果
                final imageData = event['imageData'] as List<dynamic>?;
                final confidence = event['confidence'] as double?;
                final timestamp = event['timestamp'] as int?;
                
                if (imageData != null && confidence != null) {
                  setState(() {
                    _detectedFaceImage = Uint8List.fromList(imageData.cast<int>());
                    _lastFaceConfidence = confidence;
                  });
                  
                  print('收到高置信度人脸图片，置信度: $confidence');
                }
              }
            },
            onError: (dynamic error) {
              print('EventChannel错误: $error');
            },
            onDone: () {
              print('EventChannel流结束');
              setState(() {
                _isEventChannelActive = false;
              });
            },
          );
      
      // 启动EventChannel
      _startEventChannel();
    } catch (e) {
      print('EventChannel初始化错误: $e');
    }
  }

  // 启动EventChannel
  void _startEventChannel() async {
    try {
      await _methodChannel.invokeMethod('startEventStream');
      setState(() {
        _isEventChannelActive = true;
      });
      print('EventChannel已启动');
    } catch (e) {
      print('启动EventChannel错误: $e');
    }
  }

  // 停止EventChannel
  void _stopEventChannel() async {
    try {
      await _methodChannel.invokeMethod('stopEventStream');
      setState(() {
        _isEventChannelActive = false;
        _detectedFaceImage = null;
      });
      print('EventChannel已停止');
    } catch (e) {
      print('停止EventChannel错误: $e');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // 应用进入后台时停止捕获帧，恢复时重新开始
    if (state == AppLifecycleState.paused) {
      _stopFrameCapture();
      _stopDetection();
    } else if (state == AppLifecycleState.resumed) {
      if (_initialized) {
        _startFrameCapture();
        if (_isFaceDetectionEnabled) {
          _startDetection();
        }
      }
    }
  }

  Future<void> _initDetector() async {
    try {
      // 检查是否已初始化，避免重复初始化
      print("📷 检查摄像头检测器初始化状态...");
      if (!FaceDetector.isInitialized) {
        print("🔧 开始初始化摄像头检测器...");
        final initStartTime = DateTime.now();
        await FaceDetector.initialize();
        final initEndTime = DateTime.now();
        final initDuration = initEndTime.difference(initStartTime).inMilliseconds;
        print("✅ 摄像头检测器初始化完成，耗时: ${initDuration}ms");
      } else {
        print("✅ 摄像头检测器已初始化，跳过初始化步骤");
      }

      print("📹 启动摄像头...");
      final cameraStartTime = DateTime.now();
      final cameraStarted = await FaceDetector.startCamera();
      final cameraEndTime = DateTime.now();
      final cameraDuration = cameraEndTime.difference(cameraStartTime).inMilliseconds;
      print("${cameraStarted ? '✅' : '❌'} 摄像头启动${cameraStarted ? '成功' : '失败'}，耗时: ${cameraDuration}ms");
      if (cameraStarted) {
        // 设置较高的JPEG质量，确保首次检测效果良好
        FaceDetector.setJpegQuality(80);
        
        // 确保使用DNN检测器而非Haar级联分类器
        FaceDetector.setUseHaarDetector(false);
        
        // 设置平滑系数 - 调低平滑系数，提高实时响应性
        FaceDetector.setSmoothingFactor(0.2); // 降低到0.2提高准确性
        _smoothingFactor = 0.2;
        
        // 获取初始处理间隔
        _detectionInterval = 80; // 较高的检测间隔，确保稳定性
        
        // 立即预加载模型
        await _preloadModel();

        // 设置初始人脸框显示状态，默认为true（显示人脸框）
        FaceDetector.setShowFaceBoxes(_showFaceBoxes);

        setState(() {
          _initialized = true;
          _jpegQuality = 80;
          _useHaarDetector = false; // 确保界面状态与实际一致
          _isFaceDetectionEnabled = false; // 默认关闭人脸检测
          _enablePrediction = true; // 默认开启预测
          _smoothingFactor = 0.2; // 平滑因子
          _isCameraRunning = true; // 摄像头默认开启
        });

        // 开始帧捕获定时器
        _startFrameCapture();
      } else {
        print("摄像头启动失败");
      }
    } catch (e) {
      print("初始化检测器时出错: $e");
    }
  }

  // 初始化人脸捕获轮询服务
  void _initFaceCapturePolling() {
    try {
      _faceCapturePolling = FaceCapturePolling();
      
      // 设置回调函数
      _faceCapturePolling!.onFaceCaptured = (imageData, confidence) {
        setState(() {
          _latestCapturedFace = imageData;
          _latestFaceConfidence = confidence;
        });

        
        // 如果百度SDK已初始化且启用了自动识别，则自动进行人脸识别
        if (_baiduSdkInitialized && _baiduRecognitionEnabled && confidence > 0.8) {
          _performBaiduRecognition(imageData);
        }
      };
      
      // 设置没有人脸时的回调 - 清除显示
      _faceCapturePolling!.onNoFaceDetected = () {
        setState(() {
          _latestCapturedFace = null;
          _latestFaceConfidence = 0.0;
        });

      };
      
      _faceCapturePolling!.onError = (error) {
        print('❌ 轮询服务错误: $error');
      };
      
      print('✅ 人脸捕获轮询服务初始化完成');
    } catch (e) {
      print('❌ 轮询服务初始化失败: $e');
    }
  }

  // 开始/停止轮询
  void _togglePolling() {
    if (_faceCapturePolling == null) {
      print('❌ 轮询服务未初始化');
      return;
    }
    
    setState(() {
      _isPollingEnabled = !_isPollingEnabled;
    });
    
    if (_isPollingEnabled) {
      _faceCapturePolling!.startPolling(
        interval: const Duration(seconds: 1), // 1秒轮询间隔，适合考勤认证
      );
      print('🔄 开始人脸捕获轮询');
    } else {
      _faceCapturePolling!.stopPolling();
      print('⏹️ 停止人脸捕获轮询');
    }
  }

  // 手动检查一次
  void _checkFaceOnce() {
    if (_faceCapturePolling == null) {
      print('❌ 轮询服务未初始化');
      return;
    }
    _faceCapturePolling!.checkOnce();
  }

  // 预加载模型以解决冷启动问题
  Future<void> _preloadModel() async {
    try {
      debugPrint('预加载DNN模型...');
      final dynamic result = FaceDetector.detectFacesOnly(); // 执行一次检测以初始化模型
      debugPrint('DNN模型预加载完成，获取到 ${result is List ? result.length : 0} 个结果');
    } catch (e) {
      debugPrint('DNN模型预加载失败: $e');
    }
  }

  void _startFrameCapture() {
    // 确保停止之前的定时器，避免重复
    _stopFrameCapture();
    
    // 使用最新的帧间隔设置
    final currentInterval = _isHighFPSEnabled ? _highFPSInterval : _frameInterval;
    
    _frameTimer = Timer.periodic(Duration(milliseconds: currentInterval), (_) {
      _frameCount++;

      // 如果当前正在处理，跳过这一帧
      if (_isProcessing) {
        _skipFrameCount++;
        return;
      }

      _getFrameFromDetector();
    });
    
    debugPrint('帧捕获已启动，间隔: ${currentInterval}ms');
  }

  void _stopFrameCapture() {
    if (_frameTimer != null) {
      debugPrint('停止帧捕获定时器');
      _frameTimer?.cancel();
      _frameTimer = null;
    }
  }

  // 启动独立的人脸检测定时器
  void _startDetection() {
    _stopDetection(); // 确保停止现有定时器
    
    // 设置正在检测标志
    _isDetecting = true;
    
    // 确保检测间隔不大于100ms
    int actualInterval = math.min(_detectionInterval, 100);
    
    _detectionTimer = Timer.periodic(Duration(milliseconds: actualInterval), (timer) {
      if (!_isFaceDetectionEnabled) {
        _stopDetection();
        return;
      }
      
      _detectFacesInCurrentFrame();
    });
    
    debugPrint('人脸检测器已启动，检测间隔: ${actualInterval}ms');
    // 立即执行一次检测 - 使用空计时器
    final dummyTimer = Timer.periodic(Duration.zero, (timer) {
      timer.cancel();
    });
    _detectFacesInCurrentFrame();
  }

  // 停止人脸检测定时器
  void _stopDetection() {
    _isDetecting = false;
    _detectionTimer?.cancel();
    _detectionTimer = null;
  }

  // 配置高帧率模式
  void _configureHighFPS(bool enabled) {
    setState(() {
      _isHighFPSEnabled = enabled;
      _frameInterval = enabled ? _highFPSInterval : _baseFrameInterval;
      
      // 重启帧捕获定时器以应用新间隔
      _stopFrameCapture();
      _startFrameCapture();
    });
  }

  // 动态调整帧率 - 修改为始终使用直通模式
  void _adjustFrameRate(int processingTime) {
    // 根据处理时间和人脸检测状态调整帧率
    // 在高帧率模式时使用固定帧间隔
    if (_isHighFPSEnabled) {
      int newInterval = _highFPSInterval;
      if (_frameInterval != newInterval) {
        _frameInterval = newInterval;
        if (_frameTimer != null) {
          _stopFrameCapture();
          _startFrameCapture();
        }
      }
      return;
    }

    // 正常模式下的动态调整
    int newInterval = _baseFrameInterval;

    if (_frameInterval != newInterval) {
      _frameInterval = newInterval;

      // 重启定时器以应用新的间隔
      if (_frameTimer != null) {
        _stopFrameCapture();
        _startFrameCapture();
      }
    }
  }

  // 更新实际帧率
  void _updateActualFPS() {
    final now = DateTime.now();
    final elapsedSeconds = now.difference(_lastFPSUpdate).inMilliseconds / 1000.0;
    
    if (elapsedSeconds > 0) {
      setState(() {
        _actualFPS = (_frameCount / elapsedSeconds).round();
        _frameCount = 0;
        _lastFPSUpdate = now;
      });
    }
  }

  // 从检测器获取帧数据
  Future<bool> _getFrameFromDetector() async {
    try {
      final start = DateTime.now();
      final Uint8List? imageData = await FaceDetector.getJpegImageData();
      if (imageData == null || imageData.isEmpty) return false;
      
      final end = DateTime.now();
      final processingTime = end.difference(start).inMilliseconds;
      
      // 更新处理时间和状态
      setState(() {
        _jpegImageData = imageData;
        _processingTime = processingTime;
        _isProcessing = false;
      });
      
      // 如果处理时间超过帧间隔，可能需要调整帧率
      if (processingTime > _frameInterval) {
        _skipFrameCount++;
        // 可以在这里添加逻辑，减少帧率以适应性能
      } else {
        _skipFrameCount = 0;
      }
      
      return true;
    } catch (e) {
      debugPrint('获取帧失败: $e');
      _isProcessing = false;
      return false;
    }
  }
  
  // 检测一帧中的人脸
  Future<bool> _detectFacesInCurrentFrame() async {
    if (!_isFaceDetectionEnabled) return false;
    
    // 初始化为检测状态
    _isDetecting = true;
    
    try {
      final start = DateTime.now();
      final List<Map<dynamic, dynamic>> faces = await FaceDetector.detectFacesOnly();
      final end = DateTime.now();
      
      final detectionTime = end.difference(start).inMilliseconds;
      
      setState(() {
        _detectionTime = detectionTime;
        
        // 记录当前帧检测的人脸
        _previousFaces = List.from(_faces);
        _faces = faces;
        _isDetecting = false;
        
        // 仅在实际检测到人脸或完全无人脸时更新预测位置
        if (faces.isNotEmpty) {
          _noFaceFrameCount = 0;
          _lastDetectedFaces = List.from(faces);
          
          // 计算预测的人脸位置
          if (_enablePrediction) {
            _updatePredictedFaces();
          } else {
            _predictedFaces = List.from(faces);
          }
        } else {
          // 当没有检测到人脸时，不立即清除显示，而是使用计数
          _noFaceFrameCount++;
          
          if (_noFaceFrameCount > _maxNoFaceFrames) {
            // 超过最大无人脸帧数，清除显示
            _lastDetectedFaces = [];
            _predictedFaces = [];
          } else if (_enablePrediction && _lastDetectedFaces.isNotEmpty) {
            // 使用上次检测到的人脸，但应用衰减
            _updatePredictedFacesWithDecay();
          }
        }
      });
      
      return true;
    } catch (e) {
      debugPrint('人脸检测失败: $e');
      setState(() {
        _isDetecting = false;
      });
      return false;
    }
  }

  // 更新预测的人脸位置，基于当前和之前的检测结果
  void _updatePredictedFaces() {
    if (_previousFaces.isEmpty || _faces.isEmpty) {
      _predictedFaces = List.from(_faces);
      return;
    }
    
    List<Map<dynamic, dynamic>> newPredictedFaces = [];
    
    // 对每个当前检测到的人脸
    for (var face in _faces) {
      // 在上一帧中寻找最近的匹配人脸
      int bestMatchIndex = _findNearestFace(face, _previousFaces);
      
      // 根据预测因子调整位置
      if (bestMatchIndex >= 0) {
        final prevFace = _previousFaces[bestMatchIndex];
        
        // 获取人脸框坐标
        final currentX = (face['x'] as num).toInt();
        final currentY = (face['y'] as num).toInt();
        final currentWidth = (face['width'] as num).toInt();
        final currentHeight = (face['height'] as num).toInt();
        
        final prevX = (prevFace['x'] as num).toInt();
        final prevY = (prevFace['y'] as num).toInt();
        final prevWidth = (prevFace['width'] as num).toInt();
        final prevHeight = (prevFace['height'] as num).toInt();
        
        // 计算移动速度 (delta)
        final deltaX = currentX - prevX;
        final deltaY = currentY - prevY;
        final deltaWidth = currentWidth - prevWidth;
        final deltaHeight = currentHeight - prevHeight;
        
        // 仅使用非常小的预测因子（0.2-0.3）来避免过度预测
        final predictionFactor = 0.2;  // 降低预测因子以提高准确性
        
        // 计算预测的位置
        final predictedX = currentX + (deltaX * predictionFactor).toInt();
        final predictedY = currentY + (deltaY * predictionFactor).toInt();
        final predictedWidth = currentWidth + (deltaWidth * predictionFactor).toInt();
        final predictedHeight = currentHeight + (deltaHeight * predictionFactor).toInt();
        
        // 创建预测的人脸信息
        final predictedFace = Map<dynamic, dynamic>.from(face);
        predictedFace['x'] = predictedX;
        predictedFace['y'] = predictedY;
        predictedFace['width'] = predictedWidth;
        predictedFace['height'] = predictedHeight;
        
        newPredictedFaces.add(predictedFace);
      } else {
        // 如果没有匹配的先前人脸，直接使用当前人脸
        newPredictedFaces.add(Map<dynamic, dynamic>.from(face));
      }
    }
    
    _predictedFaces = newPredictedFaces;
  }
  
  // 更新预测的人脸位置，当没有检测到人脸时应用衰减
  void _updatePredictedFacesWithDecay() {
    if (_lastDetectedFaces.isEmpty) return;
    
    List<Map<dynamic, dynamic>> newPredictedFaces = [];
    double decayFactor = 0.9; // 衰减系数 - 随时间降低预测位置的可信度
    
    // 获取无人脸帧数的衰减因子
    double frameDecay = math.pow(decayFactor, _noFaceFrameCount).toDouble();
    
    // 限制衰减最小值，确保不会过度衰减
    frameDecay = math.max(0.7, frameDecay); // 至少保留70%的大小
    
    for (var face in _lastDetectedFaces) {
      Map<dynamic, dynamic> predictedFace = Map.from(face);
      
      // 应用预测衰减
      final originalWidth = (predictedFace['width'] as num).toInt();
      final originalHeight = (predictedFace['height'] as num).toInt();
      
      // 随时间缩小人脸框以表示不确定性增加
      predictedFace['width'] = (originalWidth * frameDecay).toInt();
      predictedFace['height'] = (originalHeight * frameDecay).toInt();
      
      // 调整位置以保持框中心不变
      final originalX = (predictedFace['x'] as num).toInt();
      final originalY = (predictedFace['y'] as num).toInt();
      final widthDiff = originalWidth - (predictedFace['width'] as num).toInt();
      final heightDiff = originalHeight - (predictedFace['height'] as num).toInt();
      
      predictedFace['x'] = originalX + (widthDiff ~/ 2);
      predictedFace['y'] = originalY + (heightDiff ~/ 2);
      
      newPredictedFaces.add(predictedFace);
    }
    
    _predictedFaces = newPredictedFaces;
  }
  
  // 寻找最近的人脸匹配
  int _findNearestFace(Map<dynamic, dynamic> face, List<Map<dynamic, dynamic>> faceList) {
    if (faceList.isEmpty) return -1;
    
    try {
      int bestIndex = -1;
      double minDistance = double.infinity;
      
      // 当前人脸中心点
      final faceX = (face['x'] as num).toInt() + (face['width'] as num).toInt() ~/ 2;
      final faceY = (face['y'] as num).toInt() + (face['height'] as num).toInt() ~/ 2;
      
      // 查找最近的匹配
      for (int i = 0; i < faceList.length; i++) {
        try {
          final otherFace = faceList[i];
          final otherX = (otherFace['x'] as num).toInt() + (otherFace['width'] as num).toInt() ~/ 2;
          final otherY = (otherFace['y'] as num).toInt() + (otherFace['height'] as num).toInt() ~/ 2;
          
          // 计算中心点距离
          final distX = faceX - otherX;
          final distY = faceY - otherY;
          final distance = math.sqrt(distX * distX + distY * distY);
          
          if (distance < minDistance) {
            minDistance = distance;
            bestIndex = i;
          }
        } catch (e) {
          print('处理人脸坐标时出错: $e, 数据: $face');
          continue;
        }
      }
      
      // 如果距离太远，认为是不同的人脸
      return minDistance < 100 ? bestIndex : -1;
    } catch (e) {
      print('寻找最近人脸时出错: $e');
      return -1;
    }
  }

  void _adjustJpegQuality(int newQuality) {
    if (_isProcessing) return;

    FaceDetector.setJpegQuality(newQuality);
    setState(() {
      _jpegQuality = newQuality;
    });
  }

  void _adjustJpegQualityFromSlider(double value) {
    setState(() {
      _jpegQuality = value.round();
      FaceDetector.setJpegQuality(_jpegQuality);
    });
  }

  // 切换检测器类型 - 但强制只使用DNN
  void _toggleDetectorType(bool useHaar) {
    // 如果尝试切换到Haar检测器，直接拒绝
    if (useHaar) {
      debugPrint('请求使用Haar检测器被拒绝，只能使用DNN检测器');
      return;
    }
    
    setState(() {
      _useHaarDetector = false;
    });

    FaceDetector.setUseHaarDetector(false);
    debugPrint('确认使用DNN检测器');
  }

  // 切换人脸检测功能
  void _toggleFaceDetection(bool value) {
    setState(() {
      _isFaceDetectionEnabled = value;
      
      if (value) {
        // 启动检测
        _startDetection();
      } else {
        // 停止检测
        _stopDetection();
        // 清空人脸数据，这样关闭检测后就不会显示人脸框
        _faces = [];
        _predictedFaces = [];
      }
    });
  }

  // 启动自适应质量控制
  void _startAdaptiveQualityControl() {
    if (!_adaptiveQualityEnabled) return;
    
    _adaptiveQualityTimer = Timer.periodic(const Duration(milliseconds: 500), (timer) {
      if (!_initialized || !_adaptiveQualityEnabled) {
        timer.cancel();
        return;
      }
      
      _systemLoad = FaceDetector.getSystemLoad();
      
      // 根据系统负载动态调整JPEG质量和处理间隔
      if (_systemLoad > 85) {
        // 极高负载 - 强制降低质量和频率
        FaceDetector.setJpegQuality(45);
        _detectionInterval = 120;
        _frameInterval = 80; // 12.5fps
        _isPerformanceWarning = true;
      } else if (_systemLoad > 75) {
        // 高负载 - 降低质量和频率
        FaceDetector.setJpegQuality(55);
        _detectionInterval = 100;
        _frameInterval = 67; // 约15fps
        _isPerformanceWarning = true;
      } else if (_systemLoad > 60) {
        // 中高负载
        FaceDetector.setJpegQuality(65);
        _detectionInterval = 80;
        _frameInterval = 50; // 20fps
        _isPerformanceWarning = false;
      } else if (_systemLoad > 40) {
        // 中等负载 - 平衡质量和性能
        FaceDetector.setJpegQuality(75);
        _detectionInterval = 70;
        _frameInterval = 40; // 25fps
        _isPerformanceWarning = false;
      } else {
        // 低负载 - 提高质量和频率
        FaceDetector.setJpegQuality(85);
        _detectionInterval = 50;
        _frameInterval = 33; // 约30fps
        _isPerformanceWarning = false;
      }

      // 如果高帧率模式已开启，使用更高的帧率
      if (_isHighFPSEnabled) {
        _frameInterval = _highFPSInterval;
      }
      
      // 重启帧捕获定时器以应用新间隔
      if (_frameTimer != null) {
        _stopFrameCapture();
        _startFrameCapture();
      }
      
      // 重启检测定时器以应用新间隔
      if (_detectionTimer != null) {
        _stopDetection();
        _startDetection();
      }
      
      // 仅在状态可能变化时才更新UI
      if (mounted) {
        setState(() {});
      }
    });
  }
  
  // 停止自适应质量控制
  void _stopAdaptiveQualityControl() {
    _adaptiveQualityTimer?.cancel();
    _adaptiveQualityTimer = null;
  }
  
  // 设置平滑因子
  void _setSmoothingFactor(double factor) {
    FaceDetector.setSmoothingFactor(factor);
    setState(() {
      _smoothingFactor = factor;
    });
  }
  
  // 切换自适应质量
  void _toggleAdaptiveQuality(bool enabled) {
    setState(() {
      _adaptiveQualityEnabled = enabled;
    });
    
    if (enabled) {
      _startAdaptiveQualityControl();
    } else {
      _stopAdaptiveQualityControl();
    }
  }

  // 获取保存路径
  Future<String> _getSavePath(String type) async {
    // 根据类型选择不同的保存路径
    String? customPath;
    if (type == 'photo' && _photoSavePath != null && _photoSavePath!.isNotEmpty) {
      customPath = _photoSavePath;
    } else if (type == 'video' && _videoSavePath != null && _videoSavePath!.isNotEmpty) {
      customPath = _videoSavePath;
    } else if (_customSavePath != null && _customSavePath!.isNotEmpty) {
      customPath = _customSavePath;
    }
    
    // 如果用户设置了自定义路径，优先使用
    if (customPath != null && customPath.isNotEmpty) {
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      String fileName = type == 'photo' ? 'photo_$timestamp.jpg' : 'video_$timestamp.mp4';
      
      // 确保路径使用正斜杠
      String normalizedPath = customPath.replaceAll('\\', '/');
      // 确保路径以斜杠结尾
      if (!normalizedPath.endsWith('/')) {
        normalizedPath = '$normalizedPath/';
      }
      
      String fullPath = normalizedPath + fileName;
      debugPrint('生成保存路径: $fullPath');
      
      // 确保目录存在
      try {
        final Directory customDir = Directory(normalizedPath);
        if (!await customDir.exists()) {
          await customDir.create(recursive: true);
          debugPrint('创建目录: $normalizedPath');
        }
      } catch (e) {
        debugPrint('创建目录失败: $e');
      }
      
      return normalizedPath;
    }
    
    // 否则使用应用文档目录
    try {
      final Directory dir = await Directory('${Directory.systemTemp.path}/face_detector').create(recursive: true);
      final String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      
      String path = '${dir.path}/${type == 'photo' ? 'photo' : 'video'}_$timestamp.${type == 'photo' ? 'jpg' : 'mp4'}';
      // 规范化路径
      path = path.replaceAll('\\', '/');
      debugPrint('使用临时目录: $path');
      return dir.path.replaceAll('\\', '/');
    } catch (e) {
      debugPrint('创建临时目录失败: $e');
      // 失败时返回一个备用路径目录
      return Directory.systemTemp.path.replaceAll('\\', '/');
    }
  }
  
  // 拍照功能
  Future<void> _takePhoto() async {
    if (!_initialized) {
      _showMessage('摄像头未初始化');
      return;
    }
    
    try {
      final String savePath = await _getSavePath('photo');
      final bool success = await FaceDetector.takePhoto(savePath);
      
      if (success) {
        setState(() {
          _lastPhotoPath = savePath;
        });
        _showMessage('照片已保存到: $savePath');
      } else {
        _showMessage('拍照失败');
      }
    } catch (e) {
      _showMessage('拍照出错: $e');
    }
  }
  
  // 开始/停止录像
  Future<void> _toggleRecording() async {
    if (!_initialized) {
      _showMessage('摄像头未初始化');
      return;
    }
    
    try {
      if (_isRecording) {
        // 停止录像
        final bool success = await FaceDetector.stopRecording();
        if (success) {
          setState(() {
            _isRecording = false;
          });
          _showMessage('视频已保存到: $_recordingPath');
        } else {
          _showMessage('停止录像失败');
        }
      } else {
        // 开始录像
        final String savePath = await _getSavePath('video');
        final bool success = await FaceDetector.startRecording(savePath);
        
        if (success) {
          setState(() {
            _isRecording = true;
            _recordingPath = savePath;
          });
          _showMessage('开始录像...');
        } else {
          _showMessage('开始录像失败');
        }
      }
    } catch (e) {
      _showMessage('录像操作出错: $e');
    }
  }
  
  // 设置照片保存路径对话框
  Future<void> _showSetPhotoPathDialog() async {
    try {
      // 使用FilePicker让用户选择目录
      final String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择照片保存路径',
        lockParentWindow: true,
      );
      
      // 如果用户选择了目录
      if (selectedDirectory != null && selectedDirectory.isNotEmpty) {
        setState(() {
          _photoSavePath = selectedDirectory;
        });
        _showMessage('照片保存路径已设置: $_photoSavePath');
      } else {
        // 用户取消了选择
        _showMessage('未选择照片保存路径');
      }
    } catch (e) {
      _showMessage('选择照片路径出错: $e');
      print('文件选择器错误: $e');
    }
  }
  
  // 设置视频保存路径对话框
  Future<void> _showSetVideoPathDialog() async {
    try {
      // 使用FilePicker让用户选择目录
      final String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择视频保存路径',
        lockParentWindow: true,
      );
      
      // 如果用户选择了目录
      if (selectedDirectory != null && selectedDirectory.isNotEmpty) {
        setState(() {
          _videoSavePath = selectedDirectory;
        });
        _showMessage('视频保存路径已设置: $_videoSavePath');
      } else {
        // 用户取消了选择
        _showMessage('未选择视频保存路径');
      }
    } catch (e) {
      _showMessage('选择视频路径出错: $e');
      print('文件选择器错误: $e');
    }
  }
  
  // 设置保存路径对话框
  Future<void> _showSetPathDialog() async {
    try {
      // 使用FilePicker让用户选择目录
      final String? selectedDirectory = await FilePicker.platform.getDirectoryPath(
        dialogTitle: '选择保存路径',
        lockParentWindow: true,
      );
      
      // 如果用户选择了目录
      if (selectedDirectory != null && selectedDirectory.isNotEmpty) {
        setState(() {
          _customSavePath = selectedDirectory;
        });
        _showMessage('保存路径已设置: $_customSavePath');
      } else {
        // 用户取消了选择
        _showMessage('未选择保存路径');
      }
    } catch (e) {
      _showMessage('选择路径出错: $e');
      print('文件选择器错误: $e');
    }
  }
  
  // 显示消息
  void _showMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }
  
  // 开启摄像头
  Future<void> _toggleCamera() async {
    if (_isCameraRunning) {
      // 关闭摄像头
      FaceDetector.stopCamera();
      setState(() {
        _isCameraRunning = false;
        _initialized = false;
        // 清空人脸数据
        _faces = [];
        _predictedFaces = [];
      });
      _stopFrameCapture();
      _stopDetection();
    } else {
      // 开启摄像头
      try {
        // 确保检测器已初始化
        if (!FaceDetector.isInitialized) {
          print("🔧 摄像头开启前需要初始化检测器...");
          final initStartTime = DateTime.now();
          await FaceDetector.initialize();
          final initEndTime = DateTime.now();
          final initDuration = initEndTime.difference(initStartTime).inMilliseconds;
          print("✅ 检测器初始化完成，耗时: ${initDuration}ms");
        }

        print("📹 开启摄像头 $_currentCameraId...");
        final cameraStartTime = DateTime.now();
        bool success = await FaceDetector.startCameraWithID(_currentCameraId);
        final cameraEndTime = DateTime.now();
        final cameraDuration = cameraEndTime.difference(cameraStartTime).inMilliseconds;
        print("${success ? '✅' : '❌'} 摄像头开启${success ? '成功' : '失败'}，耗时: ${cameraDuration}ms");

        if (success) {
          setState(() {
            _isCameraRunning = true;
            _initialized = true;
          });
          _startFrameCapture();
          if (_isFaceDetectionEnabled) {
            _startDetection();
          }
        } else {
          _showMessage('开启摄像头失败');
        }
      } catch (e) {
        _showMessage('开启摄像头出错: $e');
      }
    }
  }
  
  // 切换摄像头
  Future<void> _switchCamera(int cameraId) async {
    if (_isCameraChanging || !_initialized) return;
    
    setState(() {
      _isCameraChanging = true;
    });
    
    try {
      // 停止当前所有操作
      _stopFrameCapture();
      _stopDetection();
      if (_isRecording) {
        await FaceDetector.stopRecording();
        setState(() {
          _isRecording = false;
        });
      }
      
      // 停止当前摄像头
      await FaceDetector.stopCamera();
      
      // 启动新摄像头
      final bool success = await FaceDetector.startCameraWithID(cameraId);
      
      if (success) {
        setState(() {
          _currentCameraId = cameraId;
          _showMessage('已切换到摄像头 $cameraId');
        });
        
        // 重新启动帧捕获和人脸检测
        _startFrameCapture();
        if (_isFaceDetectionEnabled) {
          _startDetection();
        }
      } else {
        // 如果切换失败，尝试回到之前的摄像头
        _showMessage('切换摄像头 $cameraId 失败，回到摄像头 $_currentCameraId');
        await FaceDetector.startCameraWithID(_currentCameraId);
        _startFrameCapture();
        if (_isFaceDetectionEnabled) {
          _startDetection();
        }
      }
    } catch (e) {
      _showMessage('切换摄像头出错: $e');
    } finally {
      setState(() {
        _isCameraChanging = false;
      });
    }
  }
  
  // 显示选择摄像头对话框
  Future<void> _showCameraSelectDialog() async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('选择摄像头'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('摄像头 0 (默认)'),
              selected: _currentCameraId == 0,
              onTap: () {
                Navigator.pop(context);
                _switchCamera(0);
              },
            ),
            ListTile(
              title: Text('摄像头 1'),
              selected: _currentCameraId == 1,
              onTap: () {
                Navigator.pop(context);
                _switchCamera(1);
              },
            ),
            ListTile(
              title: Text('摄像头 2'),
              selected: _currentCameraId == 2,
              onTap: () {
                Navigator.pop(context);
                _switchCamera(2);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
        ],
      ),
    );
  }

  // 百度人脸识别服务是否正在运行
  bool _isFaceRecognitionRunning = false;
  
  // 切换人脸识别功能
  void _toggleFaceRecognition() {
    setState(() {
      _baiduRecognitionEnabled = !_baiduRecognitionEnabled;
    });
    
    if (_baiduRecognitionEnabled) {
      _recognizeWithBaiduSdk();
    }
  }

  // 注册新人脸
  void _registerNewFace() async {
    if (!_baiduSdkInitialized) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('百度SDK未初始化'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }
    
    if (_latestCapturedFace == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请先捕获人脸图像'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    
    // 弹出注册对话框
    final registrationInfo = await _showRegistrationDialog();
    
    if (registrationInfo != null) {
      setState(() {
        _baiduStatusMessage = '正在注册人脸...';
      });
      
      try {
        final success = await BaiduFaceRecognition.addUser(
          _latestCapturedFace!,
          registrationInfo['userId']!,
          registrationInfo['groupId']!,
          registrationInfo['userInfo'],
        );
        
        if (success) {
          setState(() {
            _baiduFaceCount = BaiduFaceRecognition.getFaceCount();
            _baiduStatusMessage = '人脸注册成功！当前人脸库: $_baiduFaceCount';
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('人脸注册成功！'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          setState(() {
            _baiduStatusMessage = '人脸注册失败';
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('人脸注册失败'),
              backgroundColor: Colors.red,
            ),
          );
        }
      } catch (e) {
        setState(() {
          _baiduStatusMessage = '注册异常: $e';
        });
      }
    }
  }
  
  // 测试人脸识别
  void _testFaceRecognition() async {
    if (_latestCapturedFace != null) {
      setState(() {
        _baiduStatusMessage = '正在识别捕获的人脸...';
      });
      
      try {
        final results = await BaiduFaceRecognition.identifyFace(_latestCapturedFace!);
        
        setState(() {
          _baiduRecognitionResults = results;
          if (results.isEmpty) {
            _baiduStatusMessage = '未找到匹配的人脸';
          } else {
            _baiduStatusMessage = '识别成功！匹配到 ${results.length} 个人脸';
            // _showBaiduRecognitionResult(results.first);
          }
        });
      } catch (e) {
        setState(() {
          _baiduStatusMessage = '识别失败: $e';
        });
      }
    }
  }
  
  // 显示注册对话框
  Future<Map<String, String>?> _showRegistrationDialog() async {
    final TextEditingController userIdController = TextEditingController();
    final TextEditingController groupIdController = TextEditingController();
    final TextEditingController userInfoController = TextEditingController();
    
    // 设置默认值
    groupIdController.text = 'default_group';
    
    return showDialog<Map<String, String>?>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('注册人脸'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: userIdController,
                  decoration: const InputDecoration(
                    labelText: '用户ID *',
                    hintText: '请输入唯一的用户ID',
                  ),
                ),
                TextField(
                  controller: groupIdController,
                  decoration: const InputDecoration(
                    labelText: '分组ID *',
                    hintText: '请输入分组ID',
                  ),
                ),
                TextField(
                  controller: userInfoController,
                  decoration: const InputDecoration(
                    labelText: '用户信息 (可选)',
                    hintText: '请输入用户附加信息',
                  ),
                  maxLines: 3,
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                if (userIdController.text.isEmpty || groupIdController.text.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('用户ID和分组ID不能为空')),
                  );
                  return;
                }
                
                Navigator.of(context).pop({
                  'userId': userIdController.text,
                  'groupId': groupIdController.text,
                  'userInfo': userInfoController.text,
                });
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
    
    // 显示加载指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    
    try {
      // 调用识别服务
      final results = await _faceRecognitionService.recognizeWithCapturedFace(_latestCapturedFace!);
      
      // 关闭加载指示器
      Navigator.of(context).pop();
      
      if (results.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('未找到匹配的人脸')),
        );
      } else {
        // 显示结果
        final result = results.first;
        _showRecognitionResultDialog(result);
      }
    } catch (e) {
      // 关闭加载指示器
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('识别过程出错: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('人脸检测 - Windows版'),
        backgroundColor: Colors.blueGrey,
        actions: [
          // SDK配置按钮
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SdkConfigScreen(),
                ),
              );
            },
            tooltip: 'SDK配置',
          ),
          // 添加系统负载指示器
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4.0),
            child: Center(
              child: Text(
                '负载: $_systemLoad%',
                style: TextStyle(
                  color: _systemLoad > 70 ? Colors.redAccent : Colors.greenAccent,
                  fontSize: 12,
                ),
              ),
            ),
          ),
          // 添加性能警告图标
          if (_isPerformanceWarning)
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8.0),
              child: Icon(
                Icons.warning_amber_rounded,
                color: Colors.amber,
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // 摄像头预览区域
            Container(
              width: 320,  // 固定宽度
              height: 240, // 固定高度
              decoration: BoxDecoration(
                border: Border.all(color: Colors.black, width: 1),
              ),
              child: _initialized && _jpegImageData != null ? 
              Stack(
                fit: StackFit.expand,
                children: [
                  Image.memory(
                    _jpegImageData!,
                    gaplessPlayback: true, // 防止图像闪烁
                    fit: BoxFit.cover,
                  ),
                  Positioned.fill(
                    child: CustomPaint(
                      painter: FaceDetectionPainter(
                        faces: _showFaceBoxes ? (_enablePrediction && _predictedFaces.isNotEmpty ? _predictedFaces : _faces) : [],
                        showFaceBoxes: _showFaceBoxes,
                      ),
                    ),
                  ),
                ],
              ) : const Center(
                child: Text(
                  '摄像头未启动',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ),
            
            // 高置信度人脸检测结果显示区域
            if (_detectedFaceImage != null) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green, width: 2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Text(
                      '🎯 检测到高置信度人脸',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '置信度: ${(_lastFaceConfidence * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.green.shade600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: 160,
                      height: 120,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.green, width: 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.memory(
                          _detectedFaceImage!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Center(
                              child: Text('无法显示图片', style: TextStyle(color: Colors.red)),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _detectedFaceImage = null;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade600,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('清除'),
                    ),
                  ],
                ),
              ),
            ],

            // 考勤认证人脸捕获显示区域
            if (_latestCapturedFace != null) ...[
              const SizedBox(height: 16),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  border: Border.all(color: Colors.blue, width: 2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Text(
                      '🎯 考勤认证 - 人脸捕获',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '置信度: ${(_latestFaceConfidence * 100).toStringAsFixed(1)}%',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.blue.shade600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: 160,
                      height: 120,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.blue, width: 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.memory(
                          _latestCapturedFace!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Center(
                              child: Text('无法显示图片', style: TextStyle(color: Colors.red)),
                            );
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '图片大小: ${_latestCapturedFace!.length} 字节',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade500,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: () {
                        setState(() {
                          _latestCapturedFace = null;
                        });
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade600,
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('清除'),
                    ),
                  ],
                ),
              ),
            ],
            
            // 控制面板
            Container(
              color: Colors.black54,
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 添加开启/关闭摄像头按钮
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ElevatedButton(
                        onPressed: _toggleCamera,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isCameraRunning ? Colors.redAccent : Colors.tealAccent,
                        ),
                        child: Text(
                          _isCameraRunning ? '关闭摄像头' : '开启摄像头',
                          style: TextStyle(color: _isCameraRunning ? Colors.white : Colors.black),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  // 检测信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '检测到: ${_faces.length} 人脸',
                        style: const TextStyle(color: Colors.white),
                      ),
                      Text(
                        '实际帧率: ${_actualFPS}fps (目标: ${1000 ~/ _frameInterval}fps)',
                        style: const TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '视频耗时: ${_processingTime}ms',
                        style: const TextStyle(color: Colors.white70),
                      ),
                      Text(
                        '检测耗时: ${_detectionTime}ms',
                        style: const TextStyle(color: Colors.white70),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // 添加人脸检测开关
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '人脸检测:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _isFaceDetectionEnabled,
                            onChanged: _toggleFaceDetection,
                            activeColor: Colors.tealAccent,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          const Text(
                            '显示人脸框:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _showFaceBoxes,
                            onChanged: (value) {
                              setState(() {
                                _showFaceBoxes = value;
                                // 同步设置到C++层
                                final result = FaceDetector.setShowFaceBoxes(value);
                                print('切换人脸框显示状态: $value, 结果: $result');
                              });
                            },
                            activeColor: Colors.yellowAccent,
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // 高置信度人脸自动捕获开关
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '自动捕获高置信度人脸:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _isEventChannelActive,
                            onChanged: (value) {
                              if (value) {
                                _startEventChannel();
                              } else {
                                _stopEventChannel();
                              }
                            },
                            activeColor: Colors.orange,
                          ),
                        ],
                      ),
                      if (_isEventChannelActive)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '监听中',
                            style: TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // 轮询机制人脸捕获控件
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '轮询模式捕获人脸:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _isPollingEnabled,
                            onChanged: (value) {
                              _togglePolling();
                            },
                            activeColor: Colors.green,
                          ),
                        ],
                      ),
                      if (_isPollingEnabled)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Text(
                            '轮询中',
                            style: TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ),
                    ],
                  ),
                  
                  // 百度人脸识别控件
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '百度人脸识别:',
                            style: TextStyle(color: Colors.white),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _toggleFaceRecognition,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text('开始识别'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _registerNewFace,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text('注册人脸'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _latestCapturedFace != null ? _testFaceRecognition : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _latestCapturedFace != null ? Colors.deepPurple : Colors.grey,
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              minimumSize: Size.zero,
                              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            ),
                            child: const Text('识别当前人脸'),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  // 百度SDK状态显示
                  if (_baiduSdkInitialized)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade900.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '百度SDK状态: $_baiduStatusMessage',
                              style: const TextStyle(color: Colors.lightBlueAccent, fontSize: 12),
                            ),
                            if (_baiduRecognitionResults.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                '最近识别: ${_baiduRecognitionResults.first.userId} (${_baiduRecognitionResults.first.score.toStringAsFixed(1)}%)',
                                style: const TextStyle(color: Colors.white, fontSize: 12),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  
                  // 考勤认证轮询状态
                  if (_isPollingEnabled)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            '🎯 考勤认证模式: 运行中',
                            style: TextStyle(color: Colors.greenAccent, fontSize: 12),
                          ),
                          Text(
                            '轮询间隔: 1秒',
                            style: const TextStyle(color: Colors.white, fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 8),
                  
                  // 质量滑块
                  Row(
                    children: [
                      const Text(
                        '质量:',
                        style: TextStyle(color: Colors.white),
                      ),
                      Expanded(
                        child: Slider(
                          value: _jpegQuality.toDouble(),
                          min: 30,
                          max: 95,
                          divisions: 13,
                          activeColor: Colors.tealAccent,
                          label: '${_jpegQuality.round()}%',
                          onChanged: _adjustJpegQualityFromSlider,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  // 平滑因子滑块
                  Row(
                    children: [
                      const Text(
                        '平滑度:',
                        style: TextStyle(color: Colors.white),
                      ),
                      Expanded(
                        child: Slider(
                          value: _smoothingFactor,
                          min: 0.0,
                          max: 1.0,
                          divisions: 20,
                          activeColor: Colors.lightBlueAccent,
                          label: '${(_smoothingFactor * 100).round()}%',
                          onChanged: (value) {
                            setState(() {
                              _smoothingFactor = value;
                              FaceDetector.setSmoothingFactor(value);
                              // 同时更新移动预测权重
                              _faceMovementWeight = 0.5 + value * 0.3; // 随平滑度调整，取值范围0.5-0.8
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  // 自适应质量开关
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '自适应质量:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _adaptiveQualityEnabled,
                            onChanged: (value) {
                              setState(() {
                                _adaptiveQualityEnabled = value;
                              });
                            },
                            activeColor: Colors.purpleAccent,
                          ),
                        ],
                      ),
                      
                      // 性能指标
                      Text(
                        '负载: ${_isPerformanceWarning ? "高" : "正常"}',
                        style: TextStyle(
                          color: _isPerformanceWarning ? Colors.redAccent : Colors.greenAccent,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  
                  // 位置预测开关
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '位置预测:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _enablePrediction,
                            onChanged: (value) {
                              setState(() {
                                _enablePrediction = value;
                                // 当关闭预测时，清除预测数据
                                if (!value) {
                                  _predictedFaces = [];
                                }
                              });
                            },
                            activeColor: Colors.cyanAccent,
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  // 添加高帧率模式开关
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          const Text(
                            '高帧率模式:',
                            style: TextStyle(color: Colors.white),
                          ),
                          Switch(
                            value: _isHighFPSEnabled,
                            onChanged: _configureHighFPS,
                            activeColor: Colors.orangeAccent,
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  // 添加摄像头控制按钮区域
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0, bottom: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton.icon(
                          onPressed: _isCameraChanging ? null : _showCameraSelectDialog,
                          icon: Icon(Icons.switch_camera),
                          label: Text('切换摄像头'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purpleAccent,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // 添加拍照和录像按钮
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _takePhoto,
                        icon: Icon(Icons.camera_alt),
                        label: Text('拍照'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueAccent,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: _toggleRecording,
                        icon: Icon(_isRecording ? Icons.stop : Icons.videocam),
                        label: Text(_isRecording ? '停止录像' : '录像'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isRecording ? Colors.redAccent : Colors.greenAccent,
                          foregroundColor: Colors.white,
                        ),
                      ),
                      // 新增人脸识别按钮
                      IconButton(
                        onPressed: _registerNewFace,
                        icon: Icon(Icons.face_retouching_natural, color: Colors.white),
                        tooltip: '注册新人脸',
                      ),
                    ],
                  ),
                  
                  // 添加路径选择按钮行
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      ElevatedButton.icon(
                        onPressed: _showSetPhotoPathDialog,
                        icon: Icon(Icons.photo_library),
                        label: Text('设置拍照路径'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.amberAccent,
                          foregroundColor: Colors.black,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: _showSetVideoPathDialog,
                        icon: Icon(Icons.video_library),
                        label: Text('设置录像路径'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.lightGreenAccent,
                          foregroundColor: Colors.black,
                        ),
                      ),
                      ElevatedButton.icon(
                        onPressed: _showSetPathDialog,
                        icon: Icon(Icons.folder_open),
                        label: Text('选择文件夹'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orangeAccent,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  
                  // 显示当前保存路径
                  if (_customSavePath != null || _photoSavePath != null || _videoSavePath != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Container(
                        padding: EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.black26,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (_customSavePath != null && _customSavePath!.isNotEmpty) ...[
                              Text(
                                '默认保存路径:',
                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                _customSavePath!,
                                style: TextStyle(color: Colors.yellow, fontSize: 13),
                              ),
                              SizedBox(height: 4),
                            ],
                            if (_photoSavePath != null && _photoSavePath!.isNotEmpty) ...[
                              Text(
                                '照片保存路径:',
                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                _photoSavePath!,
                                style: TextStyle(color: Colors.amberAccent, fontSize: 13),
                              ),
                              SizedBox(height: 4),
                            ],
                            if (_videoSavePath != null && _videoSavePath!.isNotEmpty) ...[
                              Text(
                                '视频保存路径:',
                                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                              ),
                              Text(
                                _videoSavePath!,
                                style: TextStyle(color: Colors.lightGreenAccent, fontSize: 13),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                  
                  // 更新提示文字
                  const Text(
                    '提示: 自适应质量自动调整参数，平滑度减少抖动',
                    style: TextStyle(color: Colors.white60, fontSize: 12),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FaceDetectionPainter extends CustomPainter {
  final List<Map<dynamic, dynamic>> faces;
  final bool showFaceBoxes;
  
  FaceDetectionPainter({required this.faces, required this.showFaceBoxes});
  
  @override
  void paint(Canvas canvas, Size size) {
    if (!showFaceBoxes || faces.isEmpty) return;
    
    // 修改为白色四角框
    final facePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5  // 加粗边框
      ..color = Colors.white;  // 使用白色
      
    // 为每个检测到的人脸绘制框
    for (final face in faces) {
      try {
        // 获取人脸框属性
        final x = face['x'] as num;
        final y = face['y'] as num;
        final width = face['width'] as num;
        final height = face['height'] as num;
        
        // 创建矩形
        final faceRect = Rect.fromLTWH(
          x.toDouble(),
          y.toDouble(),
          width.toDouble(),
          height.toDouble(),
        );
        
        // 计算角线长度（边长的1/4）
        final cornerLength = (width < height ? width : height) / 4.0;
        
        // 绘制左上角
        canvas.drawLine(
          Offset(x.toDouble(), y.toDouble()),
          Offset(x.toDouble() + cornerLength, y.toDouble()),
          facePaint,
        );
        canvas.drawLine(
          Offset(x.toDouble(), y.toDouble()),
          Offset(x.toDouble(), y.toDouble() + cornerLength),
          facePaint,
        );
        
        // 绘制右上角
        canvas.drawLine(
          Offset(x.toDouble() + width.toDouble(), y.toDouble()),
          Offset(x.toDouble() + width.toDouble() - cornerLength, y.toDouble()),
          facePaint,
        );
        canvas.drawLine(
          Offset(x.toDouble() + width.toDouble(), y.toDouble()),
          Offset(x.toDouble() + width.toDouble(), y.toDouble() + cornerLength),
          facePaint,
        );
        
        // 绘制左下角
        canvas.drawLine(
          Offset(x.toDouble(), y.toDouble() + height.toDouble()),
          Offset(x.toDouble() + cornerLength, y.toDouble() + height.toDouble()),
          facePaint,
        );
        canvas.drawLine(
          Offset(x.toDouble(), y.toDouble() + height.toDouble()),
          Offset(x.toDouble(), y.toDouble() + height.toDouble() - cornerLength),
          facePaint,
        );
        
        // 绘制右下角
        canvas.drawLine(
          Offset(x.toDouble() + width.toDouble(), y.toDouble() + height.toDouble()),
          Offset(x.toDouble() + width.toDouble() - cornerLength, y.toDouble() + height.toDouble()),
          facePaint,
        );
        canvas.drawLine(
          Offset(x.toDouble() + width.toDouble(), y.toDouble() + height.toDouble()),
          Offset(x.toDouble() + width.toDouble(), y.toDouble() + height.toDouble() - cornerLength),
          facePaint,
        );
        
      } catch (e) {
        print('绘制人脸框错误: $e');
      }
    }
  }
  
  @override
  bool shouldRepaint(FaceDetectionPainter oldDelegate) {
    // 只有当人脸数据变化时才重绘
    return oldDelegate.faces != faces || oldDelegate.showFaceBoxes != showFaceBoxes;
  }
}