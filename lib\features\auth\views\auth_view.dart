import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import 'package:hardware/hardware.dart';

import '../../../core/providers/user_provider.dart';
import '../../../core/services/post_auth_handlers/door_lock_handler.dart';
import '../../../core/router/app_router.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page2.dart';
import '../../../shared/utils/asset_util.dart';
import '../../home/<USER>/home_view.dart';
import '../../login/models/operation_type.dart';
import '../models/auth_result.dart';
import '../view_models/auth_view_model.dart';
import '../services/multi_auth_manager.dart';
import 'widgets/auth_method_selector.dart';
import 'widgets/auth_result_display.dart';
import 'widgets/face_scanner_effect.dart';
import 'widgets/gradient_capsule.dart';
import 'widgets/reader_card_auth_widget.dart';
import 'widgets/identity_card.dart';
import 'widgets/auth_feedback_widget.dart';
import '../widget/AuthContainerWidget.dart';
import '../widget/JYBAuthView.dart';
import '../widget/AlipayLoginView.dart';
import '../widget/AccountLoginView.dart';
import '../widget/ReaderEletricSSCardView.dart';
import '../../face_detection/widgets/camera_preview_widget.dart';

class AuthView extends StatefulWidget {
  final AuthLoginType authLoginType;
  final AuthMethod authMethod; // 主显示的认证方式参数
  
  const AuthView({
    Key? key, 
    required this.authLoginType,
    required this.authMethod, // 主显示的认证方式
  }) : super(key: key);

  @override
  State<AuthView> createState() => _AuthViewState();
}

class _AuthViewState extends State<AuthView> {
  late AuthMethod _primaryMethod; // 主显示的认证方式
  AuthViewModel? _authViewModel;
  bool _viewModelReady = false;
  Timer? _timer;
  Timer? _inactivityTimer;
  StreamSubscription? _authResultSubscription;

  // 多认证管理器
  MultiAuthManager? _multiAuthManager;

  // 控制底部组件显示的状态变量
  bool _showBottomWidget = false;

  // 认证反馈状态
  AuthFeedbackState _feedbackState = AuthFeedbackState.idle;
  String? _feedbackUserName;
  String? _feedbackUserId;
  String? _feedbackErrorMessage;
  DateTime? _feedbackAuthTime;
  AuthMethod? _feedbackAuthMethod; // 新增：存储实际认证的方式

  // 页面无活动超时时间（默认60秒）
  final int _inactivityTimeoutSeconds = 120;

  // 摄像头组件的全局键，用于控制摄像头生命周期
  final GlobalKey _cameraKey = GlobalKey();

  // 门锁连接管理
  final Map<String, DoorRelayManager> _doorLockConnections = {};

  @override
  void initState() {
    super.initState();
    
    // 使用传入的认证方式作为主显示方式
    _primaryMethod = widget.authMethod;
    
    print('AuthView初始化 - 主认证方式: ${_getAuthMethodDisplayName(_primaryMethod)}');
    
    // 延迟执行，确保widget完全初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeMultiAuth();
        _initializeDoorLockConnections();
      }
    });
  }

  /// 初始化多认证管理器（优先使用多认证模式）
  Future<void> _initializeMultiAuth() async {
    try {
      print('尝试初始化多认证系统');
      
      // 获取多认证管理器实例
      _multiAuthManager = MultiAuthManager.instance;
      
      // 初始化管理器
      await _multiAuthManager!.initialize(context);
      
      // 检查是否有多种认证方式可用
      if (_multiAuthManager!.enabledMethods.length > 1) {
        print('检测到多种认证方式(${_multiAuthManager!.enabledMethods.length}种)，启用多认证模式');
        
        // 监听认证结果
        _authResultSubscription = _multiAuthManager!.authResultStream.listen(
          _onMultiAuthResult,
          onError: (error) {
            print('认证结果流错误: $error');
          },
        );
        
        // 监听多认证管理器的状态变化（用于UI更新）
        _multiAuthManager!.addListener(_onMultiAuthManagerStateChanged);
        
        // 立即启动所有认证方式的监听
        await _multiAuthManager!.startListening();
        
        print('多认证系统初始化完成，正在同时监听以下认证方式: ${_multiAuthManager!.enabledMethods.map((e) => _getAuthMethodDisplayName(e)).join('、')}');
        
        // 启动无操作超时计时器
        _startInactivityTimer();
        
        // 标记为准备完成
        setState(() {
          _viewModelReady = true;
        });
        
      } else if (_multiAuthManager!.enabledMethods.isNotEmpty) {
        // 只有一种认证方式，降级到单一认证模式
        print('只检测到一种认证方式，降级到单一认证模式');
        _initializeSingleAuth();
      } else {
        // 没有配置任何认证方式，使用默认的单一认证模式
        print('没有配置认证方式，使用默认单一认证模式');
        _initializeSingleAuth();
      }
    } catch (e) {
      print('初始化多认证系统失败: $e');
      // 降级到单一认证模式
      _initializeSingleAuth();
    }
  }

  /// 降级到单一认证模式
  void _initializeSingleAuth() {
    print('初始化单一认证模式 - 认证方式: ${_getAuthMethodDisplayName(_primaryMethod)}');
    
    final viewModel = Provider.of<AuthViewModel>(context, listen: false);
    viewModel.addListener(_onViewModelChanged);
    viewModel.setAuthMethod(_primaryMethod);
    
    setState(() {
      _viewModelReady = true;
    });
    
    // 启动无操作超时计时器
    _startInactivityTimer();
    
    // 延迟一点时间自动开始认证过程
    Future.delayed(Duration(seconds: 1), () {
      if (mounted) {
        _handleAuthenticate(context, viewModel);
      }
    });
  }

  /// 初始化门锁连接
  Future<void> _initializeDoorLockConnections() async {
    try {
      debugPrint('开始初始化门锁连接...');

      // 获取所有门锁配置
      final configs = DoorRelayConfigManager.getAllConfigs();

      if (configs.isEmpty) {
        debugPrint('没有配置门锁设备');
        return;
      }

      // 为每个配置的门锁建立连接
      for (final config in configs) {
        if (config.enabled) {
          await _createDoorLockConnection(config);
        }
      }

      debugPrint('门锁连接初始化完成，共建立 ${_doorLockConnections.length} 个连接');

    } catch (e) {
      debugPrint('初始化门锁连接失败: $e');
    }
  }

  /// 创建门锁连接
  Future<void> _createDoorLockConnection(DoorRelayConfig config) async {
    try {
      debugPrint('创建门锁连接: ${config.name} (${config.comPort})');

      final hardwareConfig = HardwareDoorRelayConfig(
        comPort: config.comPort,
        baudRate: config.baudRate,
        deviceAddress: config.deviceAddress,
        timeout: config.timeout,
      );

      final manager = DoorRelayManager();
      final connected = await manager.connect(hardwareConfig);

      if (connected) {
        _doorLockConnections[config.comPort] = manager;

        // 更新静态连接引用
        AuthPageDoorLockConnections.setConnections(_doorLockConnections);

        debugPrint('门锁连接成功: ${config.comPort}');
      } else {
        debugPrint('门锁连接失败: ${config.comPort}');
      }

    } catch (e) {
      debugPrint('创建门锁连接异常: ${config.comPort}, 错误: $e');
    }
  }

  /// 处理多认证管理器的认证结果
  void _onMultiAuthResult(AuthResult result) {
    if (!mounted) return;
    
    print('收到多认证结果: ${_getAuthMethodDisplayName(result.method)} - ${result.status.name}');
    
    // 重置无操作计时器
    _resetInactivityTimer();
    
    // 更新主显示认证方式为成功的方式（如果成功）
    if (result.status == AuthStatus.success) {
      _primaryMethod = result.method;
      print('认证成功，更新主显示方式为: ${_getAuthMethodDisplayName(_primaryMethod)}');
    }
    
    // 更新认证反馈状态
    _updateAuthFeedbackState(result);
  }
  
  /// 更新认证反馈状态
  void _updateAuthFeedbackState(AuthResult result) {
    print('=== 更新认证反馈状态 ===');
    print('结果状态: ${result.status}');
    print('用户姓名: ${result.userName}');
    print('用户ID: ${result.userId}');
    
    setState(() {
      _feedbackAuthTime = result.timestamp;
      _feedbackUserName = result.userName;
      _feedbackUserId = result.userId;
      _feedbackAuthMethod = result.method; // 更新实际认证方式
      
      switch (result.status) {
        case AuthStatus.success:
          _feedbackState = AuthFeedbackState.success;
          _showBottomWidget = true;
          
          print('设置反馈状态为success，显示底部组件: $_showBottomWidget');
          print('反馈状态: $_feedbackState');
          print('用户信息: $_feedbackUserName ($_feedbackUserId)');
          
          // 设置定时器，在显示详细信息一段时间后自动隐藏
          // 不要立即启动隐藏定时器，让AuthFeedbackWidget内部控制状态切换
          // _startHideTimer(8); // 8秒后隐藏（包括详细模式3秒+通行模式5秒）
          break;
          
        case AuthStatus.failureNoMatch:
        case AuthStatus.failureError:
          _feedbackState = AuthFeedbackState.failure;
          
          // 使用AuthResult中的具体错误信息
          String authMethodName = _getAuthMethodDisplayName(result.method);
          if (result.errorMessage?.isNotEmpty == true) {
            // 如果有具体的错误信息，使用它
            _feedbackErrorMessage = result.errorMessage!;
          } else {
            // 否则使用默认的错误信息
            if (result.status == AuthStatus.failureNoMatch) {
              _feedbackErrorMessage = '未找到匹配信息，请重试';
            } else {
              _feedbackErrorMessage = '系统错误，请重试';
            }
          }
          
          _showBottomWidget = true;
          
          print('设置反馈状态为failure，认证方式: $authMethodName');
          print('具体错误信息: $_feedbackErrorMessage');
          print('显示底部组件: $_showBottomWidget');
          
          // 失败信息显示3秒后隐藏
          _startHideTimer(3);
          break;
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 在多认证模式下，不需要单独初始化AuthViewModel
    if (_multiAuthManager?.enabledMethods.length == 1 || _multiAuthManager == null) {
      // 只有在单一认证模式下才初始化AuthViewModel
      if (_authViewModel == null) {
        _authViewModel = Provider.of<AuthViewModel>(context, listen: false);
        // Schedule the init call after the current frame
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // 设置初始认证方式
            _authViewModel!.setAuthMethod(_primaryMethod);
            _authViewModel!.init();
          }
        });
      }
    }
  }
  
  // 启动无操作超时计时器
  void _startInactivityTimer() {
    _inactivityTimer?.cancel();
    _inactivityTimer = Timer(Duration(seconds: _inactivityTimeoutSeconds), () {
      // 超时后回到Home页面
      if (mounted) {
        // 长时间无操作，清除用户信息
        if (_authViewModel != null) {
          _authViewModel!.clearUserInfo();
        }
        
        // 停止多认证监听
        if (_multiAuthManager != null) {
          _multiAuthManager!.stopListening();
        }
        
        // 隐藏底部组件
        setState(() {
          _showBottomWidget = false;
        });
        
        // 返回首页
        print("无操作超时，返回首页");
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        } else {
          // 如果无法pop，则使用AppNavigator导航到首页
          AppNavigator.toHome();
        }
      }
    });
  }
  
  // 重置无操作超时计时器
  void _resetInactivityTimer() {
    _startInactivityTimer();
  }
  
  /// 获取当前应显示的认证方式
  AuthMethod _getCurrentDisplayMethod() {
    // 如果有多认证管理器，使用其当前显示方式
    if (_multiAuthManager != null) {
      return _multiAuthManager!.currentDisplayMethod ?? _primaryMethod;
    }
    // 否则使用主要认证方式
    return _primaryMethod;
  }
  
  /// 多认证管理器状态变化回调（用于UI更新）
  void _onMultiAuthManagerStateChanged() {
    if (mounted) {
      setState(() {
        // 触发UI重建，以显示新的认证方式界面
      });
      print('多认证管理器状态变化，当前显示方式: ${_getAuthMethodDisplayName(_getCurrentDisplayMethod())}');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_viewModelReady) {
      return const BasePage2(
        mainWrapper: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 20),
              Text(
                '正在初始化认证系统...',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: _resetInactivityTimer, // 捕获整个页面的点击，重置无操作计时器
      onPanDown: (_) => _resetInactivityTimer(), // 捕获滑动开始，重置无操作计时器
      child: _multiAuthManager?.enabledMethods.length != null && _multiAuthManager!.enabledMethods.length > 1
          ? _buildMultiAuthView()
          : _buildSingleAuthView(),
    );
  }

  /// 构建多认证模式视图
  Widget _buildMultiAuthView() {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: _buildMultiAuthMainContent(),
    );
  }

  /// 构建单一认证模式视图
  Widget _buildSingleAuthView() {
    return Consumer<AuthViewModel>(
      builder: (context, viewModel, _) {
        // 确保_primaryMethod与viewModel保持同步
        _primaryMethod = viewModel.currentMethod;
        
        return BasePage2(
          topWrapper: _buildHeader(),
          mainWrapper: _buildSingleAuthMainContent(viewModel),
        );
      },
    );
  }
  
  Widget _buildHeader() {
    return const HeaderWidget();
  }

  /// 构建多认证模式的主内容
  Widget _buildMultiAuthMainContent() {
    return Container(
      child: Column(
        children: [
          // 主认证区域（显示当前认证方式的界面）
          _buildAuthAreaByMethod(_getCurrentDisplayMethod()),
          
          // SizedBox(height: 40.p),
          
          // 中间显示当前活跃的认证方式信息
          // Expanded(
          //   child: Center(
          //     child: _multiAuthManager?.isRunning == true
          //         ? _buildActiveAuthMethodsDisplay()
          //         : SizedBox(),
          //   ),
          // ),
          
          // 底部组件：根据认证状态显示不同内容
          _buildMultiAuthBottomSection(),
          SizedBox(height: 20.p),
        ],
      ),
    );
  }

  /// 构建单一认证模式的主内容  
  Widget _buildSingleAuthMainContent(AuthViewModel viewModel) {
    return Container(
      child: Column(
        children: [
          // 认证区域
          _buildAuthAreaByMethod(viewModel.currentMethod),
          
          SizedBox(height: 40.p),
          
          // 底部信息区域
          Expanded(
            child: SizedBox(),
          ),
          
          // 底部组件：根据认证状态显示不同内容
          _buildSingleAuthBottomSection(viewModel),
          SizedBox(height: 20.p),
        ],
      ),
    );
  }

  /// 构建多认证状态指示器
  Widget _buildMultiAuthStatusIndicator() {
    if (_multiAuthManager == null) return SizedBox();
    
    final enabledMethods = _multiAuthManager!.enabledMethods;
    if (enabledMethods.length <= 1) return SizedBox();
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 40.p, vertical: 20.p),
      padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
      decoration: BoxDecoration(
        color: Color(0xFF12215F).withOpacity(0.8),
        borderRadius: BorderRadius.circular(20.p),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.sensors,
            color: Color(0xFF4F83FC),
            size: 24.p,
          ),
          SizedBox(width: 10.p),
          Text(
            '多重认证已启用 (${enabledMethods.length}种方式同时运行)',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24.p,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建活跃认证方式显示
  Widget _buildActiveAuthMethodsDisplay() {
    if (_multiAuthManager == null) return SizedBox();
    
    final enabledMethods = _multiAuthManager!.enabledMethods;
    
    return Container(
      padding: EdgeInsets.all(20.p),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '正在同时监听以下认证方式',
            style: TextStyle(
              color: Colors.white,
              fontSize: 28.p,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 20.p),
          
          // 实时状态指示器网格
          // _buildAuthMethodGrid(enabledMethods),
          
          SizedBox(height: 20.p),
          
          // 状态统计信息
          // _buildStatusSummary(enabledMethods),
          
          SizedBox(height: 20.p),
          Text(
            '请使用任意一种方式进行认证',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 24.p,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建认证方式网格
  Widget _buildAuthMethodGrid(List<AuthMethod> enabledMethods) {
    return Container(
      constraints: BoxConstraints(maxWidth: 600.p),
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: enabledMethods.length > 2 ? 2 : enabledMethods.length,
          childAspectRatio: 2.5,
          crossAxisSpacing: 15.p,
          mainAxisSpacing: 15.p,
        ),
        itemCount: enabledMethods.length,
        itemBuilder: (context, index) {
          return _buildEnhancedMethodIndicator(enabledMethods[index]);
        },
      ),
    );
  }

  /// 构建增强的认证方式指示器
  Widget _buildEnhancedMethodIndicator(AuthMethod method) {
    final isActive = _multiAuthManager?.currentAuthMethod == method;
    final isPrimary = method == _primaryMethod;
    final authService = _multiAuthManager?.getAuthService(method);
    
    String label;
    IconData icon;
    String statusText;
    Color statusColor;
    
    switch (method) {
      case AuthMethod.face:
        label = '人脸识别';
        icon = Icons.face;
        break;
      case AuthMethod.idCard:
        label = '身份证';
        icon = Icons.credit_card;
        break;
      case AuthMethod.readerCard:
        label = '读者证';
        icon = Icons.card_membership;
        break;
      case AuthMethod.qrCode:
        label = '二维码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.socialSecurityCard:
        label = '社保卡';
        icon = Icons.credit_card;
        break;
      case AuthMethod.citizenCard:
        label = '市民卡';
        icon = Icons.badge;
        break;
      case AuthMethod.eletricSocialSecurityCard:
        label = '电子社保卡';
        icon = Icons.credit_card;
        break;
      case AuthMethod.wechatQRCode:
        label = '微信二维码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.wechatScanQRCode:
        label = '微信扫码';
        icon = Icons.qr_code_scanner;
        break;
      case AuthMethod.alipayQRCode:
        label = '支付宝二维码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.aliCreditQRCode:
        label = '芝麻信用码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.huiwenQRCode:
        label = '汇文二维码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.shangHaiQRCode:
        label = '上海随申码';
        icon = Icons.qr_code;
        break;
      case AuthMethod.keyboardInput:
        label = '手动输入';
        icon = Icons.keyboard;
        break;
      case AuthMethod.tencentTCard:
        label = '腾讯E证通';
        icon = Icons.phone_android;
        break;
      case AuthMethod.imiAuth:
        label = 'IMI身份认证';
        icon = Icons.fingerprint;
        break;
      case AuthMethod.takePhoto:
        label = '拍照认证';
        icon = Icons.camera_alt;
        break;
      case AuthMethod.wechatOrAlipay:
        label = '微信/支付宝';
        icon = Icons.payment;
        break;
      case AuthMethod.alipayQRCodeCredit:
        label = '支付宝信用认证';
        icon = Icons.qr_code;
        break;
      case AuthMethod.jieYueBao:
        label = '借阅宝';
        icon = Icons.book;
        break;
    }
    
    // 根据服务状态确定显示状态
    if (authService?.isListening == true) {
      statusText = '监听中';
      statusColor = Colors.green;
    } else if (authService?.isInitialized == true) {
      statusText = '已就绪';
      statusColor = Colors.blue;
    } else {
      statusText = '离线';
      statusColor = Colors.grey;
    }
    
    return Container(
      padding: EdgeInsets.all(12.p),
      decoration: BoxDecoration(
        color: isPrimary 
            ? Color(0xFF4F83FC)
            : isActive 
                ? Color(0xFF4F83FC).withOpacity(0.7)
                : Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(15.p),
        border: isPrimary 
            ? Border.all(color: Colors.white, width: 2) 
            : Border.all(color: Colors.white.withOpacity(0.3), width: 1),
        boxShadow: isPrimary ? [
          BoxShadow(
            color: Color(0xFF4F83FC).withOpacity(0.3),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ] : null,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 24.p,
              ),
              SizedBox(width: 8.p),
              Text(
                label,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.p,
                  fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.p),
          
          // 状态指示器行
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // 状态指示点
              Container(
                width: 8.p,
                height: 8.p,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: statusColor,
                ),
              ),
              SizedBox(width: 6.p),
              
              // 状态文本
              Text(
                statusText,
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14.p,
                ),
              ),
              
              // 活动指示器
              if (authService?.isListening == true) ...[
                SizedBox(width: 8.p),
                SizedBox(
                  width: 12.p,
                  height: 12.p,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isPrimary ? Colors.white : Colors.white70,
                    ),
                  ),
                ),
              ],
            ],
          ),
          
          // 主要认证方式标记
          if (isPrimary) ...[
            SizedBox(height: 4.p),
            Text(
              '主要方式',
              style: TextStyle(
                color: Colors.white,
                fontSize: 12.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建状态统计信息
  Widget _buildStatusSummary(List<AuthMethod> enabledMethods) {
    int activeCount = 0;
    int readyCount = 0;
    
    for (final method in enabledMethods) {
      final service = _multiAuthManager?.getAuthService(method);
      if (service?.isListening == true) {
        activeCount++;
      } else if (service?.isInitialized == true) {
        readyCount++;
      }
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.p, vertical: 10.p),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(20.p),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildStatusItem('活跃', activeCount, Colors.green),
          SizedBox(width: 20.p),
          _buildStatusItem('就绪', readyCount, Colors.blue),
          SizedBox(width: 20.p),
          _buildStatusItem('总计', enabledMethods.length, Colors.white),
        ],
      ),
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, int count, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 8.p,
          height: 8.p,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
          ),
        ),
        SizedBox(width: 6.p),
        Text(
          '$label: $count',
          style: TextStyle(
            color: Colors.white70,
            fontSize: 16.p,
          ),
        ),
      ],
    );
  }

  // 根据认证方式返回相应的认证区域Widget
  Widget _buildAuthAreaByMethod(AuthMethod method) {
    print('构建认证区域，认证方式: $method');
    
    switch (method) {
      case AuthMethod.face:
        return _buildFaceAuthArea();
      case AuthMethod.idCard:
        return _buildIDCardAuthArea();
      case AuthMethod.readerCard:
        return _buildReaderCardAuthArea();
      case AuthMethod.qrCode:
        return _buildQRCodeAuthArea();
      case AuthMethod.socialSecurityCard:
        return _buildSocialSecurityCardAuthArea();
      case AuthMethod.citizenCard:
        return _buildCitizenCardAuthArea();
      case AuthMethod.eletricSocialSecurityCard:
        return _buildEletricSocialSecurityCardAuthArea();
      case AuthMethod.wechatQRCode:
        return _buildWechatQRCodeAuthArea();
      case AuthMethod.wechatScanQRCode:
        return _buildWechatScanQRCodeAuthArea();
      case AuthMethod.alipayQRCode:
        return _buildAlipayQRCodeAuthArea();
      case AuthMethod.aliCreditQRCode:
        return _buildAliCreditQRCodeAuthArea();
      case AuthMethod.huiwenQRCode:
        return _buildHuiwenQRCodeAuthArea();
      case AuthMethod.shangHaiQRCode:
        return _buildShangHaiQRCodeAuthArea();
      case AuthMethod.keyboardInput:
        return _buildKeyboardInputAuthArea();
      case AuthMethod.tencentTCard:
        return _buildTencentTCardAuthArea();
      case AuthMethod.imiAuth:
        return _buildIMIAuthArea();
      case AuthMethod.takePhoto:
        return _buildTakePhotoAuthArea();
      case AuthMethod.wechatOrAlipay:
        return _buildWechatOrAlipayAuthArea();
      case AuthMethod.alipayQRCodeCredit:
        return _buildAlipayQRCodeCreditAuthArea();
      case AuthMethod.jieYueBao:
        return _buildJieYueBaoAuthArea();
    }
  }

  // 人脸认证区域
  Widget _buildFaceAuthArea() {
    return Container(
      width: 620.p,
      height: 620.p,
      margin: EdgeInsets.only(top: 126.p),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('circle_border')),
        ),
      ),
      child: Center(
        child: Container(
          margin: EdgeInsets.only(
              left: 20.p, right: 20.p, top: 20.p, bottom: 33.p),
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            color: Color(0xFF12215F),
          ),
          child: ClipOval(
            child: Stack(
              children: [
                // 背景层：摄像头实时画面
                CameraPreviewWidget(
                  key: _cameraKey,
                  width: 620.p - 40.p, // 减去左右margin
                  height: 620.p - 53.p, // 减去上下margin
                  enableFaceDetection: true,
                  enableFaceRecognition: _primaryMethod == AuthMethod.face, // 只有人脸认证模式才启用识别
                  autoStart: true,
                  onFrameUpdate: (frameData) {
                    // 可以在这里处理帧数据，比如用于人脸识别
                    if (frameData != null) {
                      // 重置无操作计时器，因为摄像头有活动
                      _resetInactivityTimer();
                    }
                  },
                  onFacesDetected: (faces) {
                    // 可以在这里处理检测到的人脸
                    if (faces.isNotEmpty) {
                      print('AuthView: 检测到 ${faces.length} 个人脸');
                      // 重置无操作计时器，因为检测到人脸活动
                      _resetInactivityTimer();
                    }
                  },
                  onFaceRecognized: (userId, userInfo) {
                    // 人脸识别成功回调
                    print('AuthView: 人脸识别成功，用户ID: $userId');
                    // 触发认证流程
                    if (_primaryMethod == AuthMethod.face) {
                      _handleFaceRecognitionSuccess(userId, userInfo);
                    }
                  },
                ),
                // 前景层：扫描效果
                FaceScannerEffect(
                  size: 620.p - 53.p, // 调整大小以匹配内容区域
                  startColor: const Color(0xFF4F83FC),
                  endColor: const Color(0xFF4F83FC),
                  startOpacity: 0.4,
                  endOpacity: 0.05,
                  borderColor: Colors.transparent,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 身份证认证区域
  Widget _buildIDCardAuthArea() {
    debugPrint('构建身份证认证界面');
    
    return SizedBox(
      width: 620.p,
      height: 746.p,
      child: ReaderCardAuthWidget(AuthLoginType.IDCard),
    );
  }

  // 读者证认证区域
  Widget _buildReaderCardAuthArea() {
    debugPrint('构建读者证认证界面');
    
    return SizedBox(
      width: 620.p,
      height: 746.p,
      child: ReaderCardAuthWidget(AuthLoginType.readerCard),
    );
  }

  // 二维码认证区域
  Widget _buildQRCodeAuthArea() {
    return Container(
      width: 620.p,
      height: 620.p,
      margin: EdgeInsets.only(top: 126.p),
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(color: const Color(0xFF4F83FC), width: 2.p),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_scanner,
              size: 200.p,
              color: const Color(0xFF4F83FC),
            ),
            SizedBox(height: 20.p),
            Text(
              '请将二维码对准扫描区域',
              style: TextStyle(
                color: Colors.white,
                fontSize: 32.p,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 社保卡认证区域
  Widget _buildSocialSecurityCardAuthArea() {
    return SizedBox(
      width: 620.p,
      height: 746.p,
      child: ReaderCardAuthWidget(AuthLoginType.socailSecurityCard),
    );
  }

  // 市民卡认证区域
  Widget _buildCitizenCardAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.citizenCard,
    );
  }

  // 电子社保卡认证区域
  Widget _buildEletricSocialSecurityCardAuthArea() {
    return ReaderEletricSSCardView(
      AuthLoginType.eletricSocialSecurityCard,
      onSubmitted: (content) {
        // 处理电子社保卡认证内容
        _handleAuthContent(content, AuthMethod.eletricSocialSecurityCard);
      },
    );
  }

  // 微信二维码认证区域
  Widget _buildWechatQRCodeAuthArea() {
    return ReaderEletricSSCardView(
      AuthLoginType.wechatQRCode,
      onSubmitted: (content) {
        // 处理微信二维码认证内容
        _handleAuthContent(content, AuthMethod.wechatQRCode);
      },
    );
  }

  // 微信扫码认证区域
  Widget _buildWechatScanQRCodeAuthArea() {
    return ReaderEletricSSCardView(
      AuthLoginType.wechatScanQRCode,
      onSubmitted: (content) {
        // 处理微信扫码认证内容
        _handleAuthContent(content, AuthMethod.wechatScanQRCode);
      },
    );
  }

  // 支付宝二维码认证区域
  Widget _buildAlipayQRCodeAuthArea() {
    return AlipayLoginView('');
  }

  // 芝麻信用码认证区域
  Widget _buildAliCreditQRCodeAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.aliCreditQRCode,
    );
  }

  // 汇文二维码认证区域
  Widget _buildHuiwenQRCodeAuthArea() {
    return ReaderEletricSSCardView(
      AuthLoginType.huiwenQRCode,
      onSubmitted: (content) {
        // 处理汇文二维码认证内容
        _handleAuthContent(content, AuthMethod.huiwenQRCode);
      },
    );
  }

  // 上海随申码认证区域
  Widget _buildShangHaiQRCodeAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.shangHaiQRCode,
    );
  }

  // 手动输入认证区域
  Widget _buildKeyboardInputAuthArea() {
    return AccountLoginView(
      callback: (account) {
        // 处理手动输入认证
        _handleAuthContent(account, AuthMethod.keyboardInput);
      },
    );
  }

  // 腾讯E证通认证区域
  Widget _buildTencentTCardAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.tencentTCard,
    );
  }

  // IMI身份认证区域
  Widget _buildIMIAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.IMIAuth,
    );
  }

  // 拍照认证区域
  Widget _buildTakePhotoAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.takePhoto,
    );
  }

  // 微信/支付宝认证区域
  Widget _buildWechatOrAlipayAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.wecharOrAlipay,
    );
  }

  // 支付宝信用认证区域
  Widget _buildAlipayQRCodeCreditAuthArea() {
    return AuthContainerWidget(
      authLoginType: AuthLoginType.alipayQRCode_credit,
    );
  }

  // 借阅宝认证区域
  Widget _buildJieYueBaoAuthArea() {
    return JYBAuthView(
      authLoginType: AuthLoginType.jieYueBao,
    );
  }

  // 处理认证内容的通用方法
  void _handleAuthContent(String content, AuthMethod method) {
    if (_multiAuthManager != null) {
      // 如果使用多认证管理器，通过管理器处理
      // 这里需要根据具体的多认证管理器API来实现
      print('通过多认证管理器处理认证内容: $content, 方法: $method');
    } else if (_authViewModel != null) {
      // 如果使用单一认证，通过ViewModel处理
      print('通过认证ViewModel处理认证内容: $content, 方法: $method');
      // 这里可以调用对应的认证方法
    }
  }

  // 认证结果区域
  Widget _buildResultView(AuthViewModel viewModel) {
    return Center(
      child: Container(
        width: 800.p,
        padding: EdgeInsets.all(40.p),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.p),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 状态图标
            Icon(
              viewModel.authStatus == AuthStatus.success
                  ? Icons.check_circle
                  : Icons.error,
              size: 100.p,
              color: viewModel.authStatus == AuthStatus.success
                  ? Colors.green
                  : Colors.red,
            ),
            SizedBox(height: 30.p),
            
            // 状态文本
            Text(
              viewModel.authStatus == AuthStatus.success
                  ? '认证成功'
                  : '认证失败',
              style: TextStyle(
                fontSize: 40.p,
                fontWeight: FontWeight.bold,
                color: viewModel.authStatus == AuthStatus.success
                    ? Colors.green
                    : Colors.red,
              ),
            ),
            SizedBox(height: 20.p),
            
            // 错误信息
            if (viewModel.errorMessage != null && viewModel.errorMessage!.isNotEmpty)
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10.p),
                child: Text(
                  viewModel.errorMessage!,
                  style: TextStyle(
                    fontSize: 28.p,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            SizedBox(height: 40.p),
            
            // 返回按钮
            ElevatedButton(
              onPressed: () => viewModel.resetAuth(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF4F83FC),
                padding: EdgeInsets.symmetric(horizontal: 40.p, vertical: 15.p),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30.p),
                ),
              ),
              child: Text(
                '返回',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 32.p,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 底部身份信息展示区
  // Widget _buildAccess() {
  //   // 优先使用多认证管理器的结果
  //   if (_multiAuthManager?.currentAuthMethod != null) {
  //     return _buildMultiAuthAccessCard();
  //   }
    
  //   // 降级使用单一认证模式的结果
  //   return _buildSingleAuthAccessCard();
  // }
  
  /// 构建多认证模式的身份卡片
  // Widget _buildMultiAuthAccessCard() {
  //   // 这里可以通过其他方式获取用户信息
  //   // 比如从全局状态、数据库或其他服务
  //   final userProvider = Provider.of<UserProvider>(context, listen: false);
    
  //   String userName = "认证用户";
  //   String userId = "未知ID";
    
  //   // 尝试从UserProvider获取信息
  //   if (userProvider.currentUser != null) {
  //     userName = userProvider.currentUser!.PersonName ?? "认证用户";
  //     userId = userProvider.currentUser!.PatronIdentifier ?? "未知ID";
  //   }
    
  //   // 获取当前时间作为认证时间
  //   final now = DateTime.now();
  //   final timeStr = "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} "
  //                  "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}";
    
  //   debugPrint("多认证模式显示卡片: 用户名=$userName, ID=$userId, 认证方式=${_multiAuthManager?.currentAuthMethod?.name}");
    
  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 40.p),
  //     child: Column(
  //       children: [
  //         // 认证方式指示
  //         Container(
  //           margin: EdgeInsets.only(bottom: 10.p),
  //           padding: EdgeInsets.symmetric(horizontal: 15.p, vertical: 5.p),
  //           decoration: BoxDecoration(
  //             color: Color(0xFF4F83FC),
  //             borderRadius: BorderRadius.circular(15.p),
  //           ),
  //           child: Text(
  //             '${_getAuthMethodDisplayName(_multiAuthManager!.currentAuthMethod!)}认证成功',
  //             style: TextStyle(
  //               color: Colors.white,
  //               fontSize: 20.p,
  //               fontWeight: FontWeight.bold,
  //             ),
  //           ),
  //         ),
  //         // 身份卡片
  //         IdentityCard(
  //           isPassMode: false,
  //           name: userName,
  //           idNumber: userId,
  //           time: timeStr,
  //           avatarUrl: AssetUtil.fullPath('avatar_placeholder'),
  //         ),
  //       ],
  //     ),
  //   );
  // }
  
  /// 构建单一认证模式的身份卡片
  // Widget _buildSingleAuthAccessCard() {
  //   final viewModel = Provider.of<AuthViewModel>(context, listen: false);
    
  //   // 从readerInfo中获取认证信息
  //   String userName = "未知用户";
  //   String userId = "未知ID";
    
  //   // 空数据检查 - 如果没有用户数据，返回空组件
  //   if ((viewModel.readerInfo == null || viewModel.readerInfo!.PersonName == null) && 
  //       (viewModel.userName == null || viewModel.userName!.isEmpty)) {
  //     debugPrint("缺少用户数据，不显示底部卡片");
  //     return const SizedBox.shrink();
  //   }
    
  //   if (viewModel.readerInfo != null) {
  //     // 使用从服务器获取的读者信息
  //     userName = viewModel.readerInfo!.PersonName ?? "未知用户";
  //     userId = viewModel.readerInfo!.PatronIdentifier ?? "未知ID";
  //   } else {
  //     // 备用：使用viewModel中保存的简单信息
  //     userName = viewModel.userName ?? "未知用户";
  //     userId = viewModel.userId ?? "未知ID";
  //   }
    
  //   // 获取当前时间作为认证时间
  //   final now = DateTime.now();
  //   final timeStr = "${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} "
  //                  "${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}:${now.second.toString().padLeft(2, '0')}";
    
  //   debugPrint("单一认证模式显示卡片: 用户名=$userName, ID=$userId, 时间=$timeStr");
    
  //   return Padding(
  //     padding: EdgeInsets.symmetric(horizontal: 40.p),
  //     child: IdentityCard(
  //       isPassMode: false,
  //       name: userName,
  //       idNumber: userId,
  //       time: timeStr,
  //       avatarUrl: AssetUtil.fullPath('avatar_placeholder'),
  //     ),
  //   );
  // }
  
  /// 获取认证方式的显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '芝麻信用码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海随申码';
      case AuthMethod.keyboardInput:
        return '手动输入';
      case AuthMethod.tencentTCard:
        return '腾讯E证通';
      case AuthMethod.imiAuth:
        return 'IMI身份认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信/支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用认证';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }

  // 根据认证状态显示不同的底部组件
  // Widget _buildBottomSection(AuthViewModel viewModel) {
  //   // 如果开始新的认证，先重置显示状态
  //   if (viewModel.isAuthenticating) {
  //     _timer?.cancel(); // 取消之前的显示定时器
  //     _showBottomWidget = false; // 重置显示状态
  //     return const SizedBox.shrink(); // 认证中不显示任何底部组件
  //   }
    
  //   // 显示底部组件的逻辑
  //   Widget bottomWidget = const SizedBox.shrink();
    
  //   // 调试输出
  //   debugPrint('底部组件状态: 显示=$_showBottomWidget, 认证状态=${viewModel.authStatus}');
    
  //   // 仅在需要显示时（由_showBottomWidget控制）创建组件
  //   if (_showBottomWidget) {
  //     // 认证成功显示身份信息
  //     if (viewModel.authStatus == AuthStatus.success) {
  //       debugPrint('显示成功认证信息');
  //       bottomWidget = _buildAccess();
  //     } 
  //     // 认证失败显示警告信息
  //     else if (viewModel.lastResult != null && 
  //             (viewModel.authStatus == AuthStatus.failureNoMatch || 
  //              viewModel.authStatus == AuthStatus.failureError)) {
  //       debugPrint('显示认证失败信息');
  //       bottomWidget = _buildWarring(viewModel.errorMessage);
  //     }
  //   }
    
  //   return bottomWidget;
  // }

  /// 构建多认证模式的底部组件
  Widget _buildMultiAuthBottomSection() {
    print('=== 构建多认证底部组件 ===');
    print('显示底部组件: $_showBottomWidget');
    print('反馈状态: $_feedbackState');
    print('用户信息: $_feedbackUserName ($_feedbackUserId)');
    
    if (!_showBottomWidget) {
      print('底部组件被隐藏，返回空组件');
      return const SizedBox.shrink();
    }
    
    // 获取实际进行认证的方式，而不是主要认证方式
    AuthMethod? actualAuthMethod = _feedbackAuthMethod ?? _getCurrentDisplayMethod();
    
    print('创建AuthFeedbackWidget');
    print('实际认证方式: $actualAuthMethod');
    
    return AuthFeedbackWidget(
      state: _feedbackState,
      userName: _feedbackUserName,
      userId: _feedbackUserId,
      errorMessage: _feedbackErrorMessage,
      authMethod: actualAuthMethod, // 使用实际认证的方式
      authTime: _feedbackAuthTime,
      detailDisplayDuration: const Duration(seconds: 3), // 详细信息显示3秒
      onStateChanged: () {
        print('认证反馈状态切换到通行模式，启动5秒隐藏定时器');
        _startHideTimer(5); // 通行模式显示5秒后隐藏
      },
    );
  }

  /// 构建单一认证模式的底部组件
  Widget _buildSingleAuthBottomSection(AuthViewModel viewModel) {
    if (!_showBottomWidget) {
      return const SizedBox.shrink();
    }
    
    return AuthFeedbackWidget(
      state: _feedbackState,
      userName: _feedbackUserName,
      userId: _feedbackUserId,
      errorMessage: _feedbackErrorMessage,
      authMethod: _primaryMethod,
      authTime: _feedbackAuthTime,
      detailDisplayDuration: const Duration(seconds: 3), // 详细信息显示3秒
      onStateChanged: () {
        print('认证反馈状态切换到通行模式，启动5秒隐藏定时器');
        _startHideTimer(5); // 通行模式显示5秒后隐藏
      },
    );
  }

  // 启动隐藏定时器
  void _startHideTimer(int seconds) {
    // 如果已经有定时器在运行，先取消它
    _timer?.cancel();
    
    // 启动新的定时器
    _timer = Timer(Duration(seconds: seconds), () {
      if (mounted) {
        setState(() {
          _showBottomWidget = false; // 隐藏底部组件
          _feedbackState = AuthFeedbackState.idle; // 重置反馈状态
          _feedbackUserName = null;
          _feedbackUserId = null;
          _feedbackErrorMessage = null;
          _feedbackAuthTime = null;
          _feedbackAuthMethod = null; // 重置认证方式
        });
        
        // 在多认证模式下，还原到默认显示方式
        if (_multiAuthManager != null && _multiAuthManager!.hasMultipleAuthMethods) {
          _multiAuthManager!.restoreToDefaultDisplay();
          print('认证完成，还原到默认显示方式');
        }
        
        // 对于单一认证方式或非人脸认证，认证完成后返回首页
        if (_multiAuthManager == null || 
            !_multiAuthManager!.hasMultipleAuthMethods ||
            (widget.authMethod != null && widget.authMethod != AuthMethod.face)) {
          debugPrint("认证完成，返回首页");
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          } else {
            // 如果无法pop，则使用AppNavigator导航到首页
            AppNavigator.toHome();
          }
        }
      }
    });
  }
  
  // 警告信息组件
  Widget _buildWarring(String? errorMessage) {
    return GradientCapsule(
      width: 480.p,
      height: 70.p,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 警告图标
          Image(
            width: 34.p,
            height: 30.p,
            image: AssetImage(AssetUtil.fullPath('serious_warning'))
          ),
          SizedBox(width: 20.p),
          // 错误信息文本
          Text(
            errorMessage ?? '认证失败，请重试',
            style: TextStyle(
              color: const Color(0xFFE13841),
              fontSize: 32.p,
            ),
          ),
        ],
      ),
    );
  }

  void _handleAuthenticate(BuildContext context, AuthViewModel viewModel) {
    // 重置无操作计时器
    _resetInactivityTimer();
    
    // 取消当前的结果显示定时器，立即清除先前的结果显示
    _timer?.cancel();
    setState(() {
      _showBottomWidget = false; // 隐藏底部组件
      _feedbackState = AuthFeedbackState.idle; // 重置反馈状态
      _feedbackUserName = null;
      _feedbackUserId = null;
      _feedbackErrorMessage = null;
      _feedbackAuthTime = null;
    });
    
    // 根据选择的认证方式执行认证
    viewModel.authenticate().then((result) {
      if (mounted) {
        setState(() {
          // 状态更新，触发UI重建
          _primaryMethod = viewModel.currentMethod;
          _showBottomWidget = true; // 显示底部组件
          
          // 确保显示底部卡片的调试输出
          print("认证结果: ${result.status}, 显示底部组件: $_showBottomWidget");
          
          // 无需在固定时间后清除用户信息，因为我们已经在新认证开始时清除
          // 但仍然设置显示时长，让底部卡片自动隐藏
          int displaySeconds = result.status == AuthStatus.success ? 5 : 3;
          _startHideTimer(displaySeconds);
        });
      }
    }).catchError((error) {
      // 处理可能的错误
      if (mounted) {
        setState(() {
          _showBottomWidget = true; // 显示底部组件（错误信息）
          _startHideTimer(3); // 错误信息显示3秒
        }); 
      }
    });
  }

  // 当ViewModel状态变化时触发
  void _onViewModelChanged() {
    if (!mounted) return;
    
    final viewModel = Provider.of<AuthViewModel>(context, listen: false);
    
    // 如果有认证结果，处理单一认证模式的反馈
    if (viewModel.lastResult != null) {
      _updateAuthFeedbackState(viewModel.lastResult!);
    }
  }

  @override
  void dispose() {
    // 清理多认证管理器
    _authResultSubscription?.cancel();
    _multiAuthManager?.stopListening();

    // 移除多认证管理器监听器
    if (_multiAuthManager != null) {
      _multiAuthManager!.removeListener(_onMultiAuthManagerStateChanged);
    }

    // 移除监听器
    if (_authViewModel != null) {
      _authViewModel!.removeListener(_onViewModelChanged);
    }

    // 清理摄像头资源
    _cleanupCameraResources();

    // 清理门锁连接
    _cleanupDoorLockConnections();

    _timer?.cancel();
    _inactivityTimer?.cancel();
    super.dispose();
  }

  /// 处理人脸识别成功
  Future<void> _handleFaceRecognitionSuccess(String userId, String? userInfo) async {
    if (!mounted) return;

    try {
      print('处理人脸识别成功: 用户ID=$userId, 用户信息=$userInfo');

      // 获取 viewModel 引用
      final viewModel = _authViewModel;
      if (viewModel == null) return;

      // 调用读者信息验证
      await viewModel.requestReaderInfo(userId, userId);

      // 检查认证结果
      if (!mounted) return;

      if (viewModel.authStatus == AuthStatus.success) {
        // 认证成功，创建成功的认证结果
        final successResult = AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.success,
          userId: viewModel.userId,
          userName: viewModel.userName,
        );

        print('人脸认证成功，更新反馈状态');
        _updateAuthFeedbackState(successResult);
      } else {
        // 认证失败，创建失败的认证结果
        final failureResult = AuthResult(
          method: AuthMethod.face,
          status: AuthStatus.failureNoMatch,
          errorMessage: '读者信息验证失败，请联系管理员',
        );

        print('人脸认证失败，更新反馈状态');
        _updateAuthFeedbackState(failureResult);
      }
    } catch (e) {
      print('处理人脸识别成功时出错: $e');

      if (!mounted) return;

      // 创建错误的认证结果
      final errorResult = AuthResult(
        method: AuthMethod.face,
        status: AuthStatus.failureError,
        errorMessage: '人脸认证处理异常: $e',
      );

      _updateAuthFeedbackState(errorResult);
    }
  }

  /// 清理摄像头资源
  void _cleanupCameraResources() {
    try {
      // 通过键获取摄像头组件并停止摄像头
      final cameraWidget = _cameraKey.currentWidget as CameraPreviewWidget?;
      if (cameraWidget != null) {
        // 如果需要，可以调用摄像头组件的停止方法
        print('AuthView: 清理摄像头资源');
      }
    } catch (e) {
      print('AuthView: 清理摄像头资源时出错: $e');
    }
  }

  /// 清理门锁连接
  void _cleanupDoorLockConnections() {
    try {
      debugPrint('开始清理门锁连接...');

      // 断开所有门锁连接
      for (final entry in _doorLockConnections.entries) {
        final comPort = entry.key;
        final manager = entry.value;

        try {
          manager.disconnect();
          debugPrint('门锁连接已断开: $comPort');
        } catch (e) {
          debugPrint('断开门锁连接时出错: $comPort, 错误: $e');
        }

      }

      // 清空本地连接映射
      _doorLockConnections.clear();

      // 清除静态连接引用
      AuthPageDoorLockConnections.clearConnections();

      debugPrint('门锁连接清理完成');

    } catch (e) {
      debugPrint('清理门锁连接时出错: $e');
    }
  }
}


