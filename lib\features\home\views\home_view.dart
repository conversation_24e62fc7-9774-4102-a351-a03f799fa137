import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:seasetting/seasetting.dart';
import 'package:provider/provider.dart';

import '../../../core/router/app_router.dart';
import '../../../core/utils/window_util.dart';
import '../../../shared/utils/asset_util.dart';
import '../../../shared/widgets/time_display.dart';
// import '../../auth/services/radar_detection_service.dart'; // 雷达功能已禁用
import '../../auth/services/auth_priority_manager.dart';

class HomeView extends StatefulWidget {
  const HomeView({super.key});

  @override
  _HomeViewState createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  int _clickCount = 0;
  Timer? _timer;

  void _onTitleTap() {
    setState(() {
      _clickCount++;
    });

    if (_clickCount >= 1) {
      _clickCount = 0;
      // AppNavigator.toAdmin();
      AppNavigator.toAdminLogin();
      return;
    }

    if (_timer != null) {
      _timer!.cancel();
    }

    _timer = Timer(const Duration(seconds: 2), () {
      setState(() {
        _clickCount = 0;
      });
    });
  }

  Widget _buildTitle() {
    return GestureDetector(
      onTap: _onTitleTap,
      child: Text(
        '自助借还书机',
        style: TextStyle(
          fontSize: 40.p,
          color: Color(0xFF222222),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SilencePage();
  }
}

class _LogoTapDetector extends StatefulWidget {
  const _LogoTapDetector({Key? key}) : super(key: key);

  @override
  State<_LogoTapDetector> createState() => _LogoTapDetectorState();
}

class _LogoTapDetectorState extends State<_LogoTapDetector> {
  int _tapCount = 0;
  Timer? _resetTimer;

  void _handleTap() {
    setState(() {
      _tapCount++;
    });

    // 重置计时器
    _resetTimer?.cancel();
    _resetTimer = Timer(const Duration(seconds: 2), () {
      setState(() {
        _tapCount = 0;
      });
    });

    // 达到5次点击
    if (_tapCount >= 5) {
      _tapCount = 0;
      _resetTimer?.cancel();
      Get.to(() => VerifyAccountPage(VerifyAccountType.local));
      // AppNavigator.toSetting();
      // AppNavigator.toAdminLogin();
    }
  }

  @override
  void dispose() {
    _resetTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _handleTap,
      child: Image.asset(
        AssetUtil.fullPath('logo.png'),
        width: 84.p,
        height: 100.p,
      ),
    );
  }
}

class SilencePage extends StatefulWidget {
  const SilencePage({super.key});

  @override
  State<SilencePage> createState() => _SilencePageState();
}

class _SilencePageState extends State<SilencePage> {
  // 雷达检测服务已禁用
  // final RadarDetectionService _radarService = RadarDetectionService.instance;
  // bool _radarInitialized = false;

  // 配置的认证方式文字
  String _authMethodsText = '人脸/读者证/身份证/二维码'; // 默认值
  late SettingProvider _settingProvider;
  bool _isConfigListenerAdded = false; // 添加标志防止重复添加监听器

  @override
  void initState() {
    super.initState();
    // 获取配置Provider
    _settingProvider = Provider.of<SettingProvider>(context, listen: false);
    // 添加配置变化监听器
    _setupConfigListener();
    // 初始化认证方式文字
    _updateAuthMethodsText();
    // 雷达检测服务已完全禁用以避免串口冲突
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 确保监听器只添加一次
    if (!_isConfigListenerAdded) {
      _setupConfigListener();
    }
  }

  /// 设置配置变化监听器
  void _setupConfigListener() {
    if (!_isConfigListenerAdded) {
      _settingProvider.addListener(_onConfigChanged);
      _isConfigListenerAdded = true;
      print('已添加配置变化监听器');
    }
  }

  /// 配置变化回调
  void _onConfigChanged() {
    print('检测到配置变化，更新认证方式显示');
    _updateAuthMethodsText();
  }

  /// 更新认证方式显示文字
  void _updateAuthMethodsText() {
    try {
      // 使用认证优先级管理器获取排序后的认证方式显示文字
      final priorityManager = AuthPriorityManager.instance;
      priorityManager.loadAuthMethods(_settingProvider);
      
      _authMethodsText = priorityManager.getEnabledAuthMethodsDisplayText();
      
      print('更新认证方式显示文字: $_authMethodsText');
      
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      print('更新认证方式文字失败: $e');
      _authMethodsText = '认证方式加载失败';
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// 获取自定义标题
  String? _getCustomTitle(String authKey) {
    try {
      final readerConfig = _settingProvider.readerConfigData;
      final authTitleConfig = readerConfig?.authTitleConfig;
      
      if (authTitleConfig != null && authTitleConfig.isNotEmpty) {
        // 通过authKey找到对应的AuthLoginType
        AuthLoginType? authType;
        for (var entry in AuthLoginMapReverse.entries) {
          if (entry.value == authKey) {
            authType = entry.key;
            break;
          }
        }
        
        if (authType != null) {
          // 查找自定义标题配置
          ReaderAuthTitleConfig? titleConfig;
          try {
            titleConfig = authTitleConfig.firstWhere(
              (element) => authType == AuthLoginTypeMap[element.type],
            );
          } catch (e) {
            titleConfig = null;
          }
          
          if (titleConfig != null && titleConfig.title != null && titleConfig.title!.isNotEmpty) {
            return titleConfig.title!;
          }
        }
      }
    } catch (e) {
      print('获取自定义标题失败: $e');
    }
    return null;
  }

  /// 雷达检测服务已禁用
  // Future<void> _initializeRadarDetection() async {
  //   // 雷达功能已完全禁用以避免串口冲突
  // }

  @override
  void dispose() {
    // 移除配置监听器
    if (_isConfigListenerAdded) {
      _settingProvider.removeListener(_onConfigChanged);
      _isConfigListenerAdded = false;
    }
    // 雷达检测服务已禁用
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 移除Consumer包装，直接返回UI
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(AssetUtil.fullPath('index_bg')),
        ),
      ),
      child: Column(
        children: [
          const HeaderWidget(),
          
          // 测试入口按钮（始终显示）
          _buildTestEntryButton(),
          
          SizedBox(height: 80.p),
          _buildTitle(),
          const Spacer(),
          GestureDetector(
            onTap: () => _navigateToAuth(),
            child: GradientContainer(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('支持开门方式：',
                        style: TextStyle(
                            fontSize: 32.p,
                            color: const Color(0xFF12215F),
                            fontWeight: FontWeight.w400,
                            height: 40 / 32)),
                            SizedBox(height: 10.p),
                    Text(_authMethodsText,
                        style: TextStyle(
                            fontSize: 32.p,
                            color: const Color(0xFF12215F),
                            fontWeight: FontWeight.w500,
                            height: 40 / 32)),
                  ],
                ),
              ),
            ),
          ),

          SizedBox(height: 116.p)
        ],
      ),
    );
  }

  /// 构建测试入口按钮
  Widget _buildTestEntryButton() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 40.p, vertical: 10.p),
      padding: EdgeInsets.symmetric(horizontal: 15.p, vertical: 8.p),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.8),
        borderRadius: BorderRadius.circular(20.p),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.settings_applications,
            color: Colors.white,
            size: 20.p,
          ),
          SizedBox(width: 8.p),
          GestureDetector(
            onTap: () {
              AppNavigator.toFaceDetectorApp();
            },
            child: Text(
            '门锁继电器测试',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20.p,
              fontWeight: FontWeight.w500,
            ),
          ),
          )
          
        ],
      ),
    );
  }

  /// 导航到认证页面
  void _navigateToAuth() {
    print('手动触发跳转到认证页面');
    AppNavigator.toAuth2();
    // AppNavigator.toFaceDetectorApp();
  }

  Widget _buildTitle() {
    return Column(
      children: [
        Text('深圳图书馆',
            style: TextStyle(
                fontSize: 54.p,
                color: const Color(0xFF12215F),
                fontWeight: FontWeight.bold)),
        Text('欢迎您',
            style: TextStyle(
                fontSize: 64.p,
                color: const Color(0xFF12215F),
                fontWeight: FontWeight.w300))
      ],
    );
  }
}

class HeaderWidget extends StatefulWidget {
  const HeaderWidget({super.key});

  @override
  State<HeaderWidget> createState() => _HeaderWidgetState();
}

class _HeaderWidgetState extends State<HeaderWidget> {
    int _tapCount = 0;
  Timer? _resetTimer;

  void _handleTap() {
    setState(() {
      _tapCount++;
    });

    // 重置计时器
    _resetTimer?.cancel();
    _resetTimer = Timer(const Duration(seconds: 2), () {
      setState(() {
        _tapCount = 0;
      });
    });

    // 达到5次点击
    if (_tapCount >= 1) {
      _tapCount = 0;
      _resetTimer?.cancel();
      // AppNavigator.toAdminLogin();
      AppNavigator.toAdmin2();
      // Get.to(() => VerifyAccountPage(VerifyAccountType.local));
      // AppNavigator.toSetting();
      // AppNavigator.toAdminLogin();
    }
  }

  @override
  void dispose() {
    _resetTimer?.cancel();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200.p,
      width: WindowUtil.width,
      // color: Colors.red,
      padding: EdgeInsets.symmetric(horizontal: 40.p),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(  
            onTap: _handleTap,
            child: Image.asset(AssetUtil.fullPath('header_left_logo'),
              width: 186.p, height: 66.p),
          ),
          const LiveTimeDisplay(),
        ],
      ),
    );
  }
}



class GradientContainer extends StatelessWidget {
  final Widget? child;

  const GradientContainer({
    Key? key,
    this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 480.p,
      height: 150.p,
      decoration: BoxDecoration(
        // 93度线性渐变
        gradient: const LinearGradient(
          begin: Alignment(-0.99, 0.12), // 近似93度角
          end: Alignment(0.99, -0.12),
          colors: [
            Color(0xFFFDE4B6), // #FDE4B6
            Color(0xFFFFD5E7), // #FFD5E7
            Color(0xFFEAE6FE), // #EAE6FE
            Color(0xFFFDEBF0), // #FDEBF0
          ],
          stops: [0.0, 0.35, 0.7, 1.0],
        ),
        // 所有四个角都是100px的圆角
        borderRadius: BorderRadius.all(Radius.circular(100.p)),
      ),
      child: child,
    );
  }
}
