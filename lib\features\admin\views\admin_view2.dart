import 'package:a3g/core/widgets/base_page2.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:async';

import '../../../core/providers/countdown_provider.dart';
import '../../../core/router/app_router.dart';
import '../../../core/router/route_extension.dart';
import '../../../core/utils/window_util.dart';
import '../../../core/widgets/base_page.dart';
import '../../../core/widgets/countdown_timer.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/footer.dart';
import '../../../core/widgets/info_panel.dart';
import '../../../core/widgets/numeric_keyboard.dart';
import '../../../shared/utils/asset_util.dart';
import '../../../shared/widgets/logo_banner.dart';
import '../../auth/views/auth_view.dart';
import '../../home/<USER>/home_view.dart';
import '../view_models/admin_view_model.dart';
import 'widgets/handleOptions.dart';

class AdminView2 extends StatelessWidget {
  const AdminView2({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => AdminViewModel(),
      child: const AdminContent(),
    );
  }
}

class AdminContent extends StatefulWidget {
  const AdminContent({super.key});

  @override
  State<AdminContent> createState() => _AdminContentState();
}

class _AdminContentState extends State<AdminContent> {

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AdminViewModel>().init();
    });
  }



  @override
  Widget build(BuildContext context) {
    return BasePage2(
      topWrapper: _buildHeader(),
      mainWrapper: _buildMainContent(),
      // bottomWrapper: Footer(
      //   child: Padding(
      //     padding: EdgeInsets.symmetric(horizontal: 80.p, vertical: 25.p),
      //     child: CustomButton.outline(
      //       text: '退出',
      //       onTap: () {
      //         AppNavigator.back();

      //       },
      //     ),
      //   ),
      // ),
    );
  }
      Widget _buildHeader() {
    return const HeaderWidget();
  }



  Widget _buildMainContent() {
    final vm = context.watch<AdminViewModel>();
    final List<HandleModel?> allTypes = [
      HandleModel(
          title: '系统配置',
          type: '',
          icon: AssetUtil.fullPath('up_icon.png'),
          onTap: () {
            AppNavigator.toSetting();
          }),
      HandleModel(
          title: '配置管理',
          type: '',
          icon: AssetUtil.fullPath('down_icon.png'),
          onTap: () {
            AppNavigator.toRemoveBook();
          }),
      HandleModel(
          title: '查询',
          type: '',
          icon: AssetUtil.fullPath('up_icon.png'),
          onTap: () {
            AppNavigator.toQuery();
          }),
      HandleModel(
          title: '结束程序',
          type: '',
          icon: AssetUtil.fullPath('slot_icon.png'),
          onTap: () {
            AppNavigator.toSlotManage();
          }),
    ];

    return HandleOptions(allTypes: allTypes,);
  }

  void _showInfoPanel(BuildContext context1) {
    final viewModel = context1.read<AdminViewModel>();
    showModalBottomSheet(
      context: context1,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      barrierColor: Colors.transparent,
      isDismissible: false,
      constraints: BoxConstraints(
        maxWidth: WindowUtil.width,
        minWidth: WindowUtil.width,
      ),
      builder: (_) => ChangeNotifierProvider.value(
        value: viewModel,
        child:  InfoPanel(
          child: _confirmContent(context1),
        ),
      ),
    );
  }
  Widget _confirmContent(BuildContext context){
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 100.p),
        Row(
          children: [
            Image.asset(
              AssetUtil.fullPath('del_icon.png'),
              width: 101.p,
              height: 98.p,
              fit: BoxFit.contain,
            ),
            SizedBox(width: 70.p),
            Text(
              '您确定要清空书箱？',
              style: TextStyle(
                  fontSize: 48.p,
                  color: const Color(0xFF222222),
                  fontWeight: FontWeight.bold,
                  height: 1),
            ),
          ],
        ),
         // SizedBox(height: 380.p),
        SizedBox(height: 180.p),

        Align(
          alignment: Alignment.centerRight,
          child: CustomButton.filled(
            width: 300.p,
            text: '确定',
            onTap: () async {
              await context.read<AdminViewModel>().resetBookCount();
              context.pop();
              context.read<CountdownProvider>().reset(50);
            },
          ),
        ),
      ],
    );

  }

  Widget _buildKeyboard(BuildContext context) {
    return NumericKeyboard(
      onValueChanged: (value) {
        // context.read<BorrowBookViewModel>().updateCardNumber(value);
      },
      onExit: () {
        AppNavigator.untilHome();
      },
      onConfirm: () => _handleConfirm(context),
    );
  }
  Future<void> _handleConfirm(BuildContext context) async {
    // final viewModel = context.read<BorrowBookViewModel>();

  }

}
