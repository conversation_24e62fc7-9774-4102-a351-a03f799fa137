import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:seasetting/seasetting.dart';
import 'package:hardware/hardware.dart';
import 'package:seaface/seaface.dart';

import '../models/auth_result.dart';
import 'auth_service_interface.dart';
import 'face_auth_service.dart';
import 'card_reader_service.dart';
import 'qr_scanner_service.dart';
import 'auth_priority_manager.dart';
import '../../../core/utils/error_recovery_manager.dart';

/// 认证状态枚举
enum MultiAuthState {
  idle,           // 空闲状态
  initializing,   // 初始化中
  listening,      // 监听中
  authenticating, // 认证中
  completed,      // 认证完成
  error          // 错误状态
}

/// 多认证方式管理器
/// 负责统一管理和协调多种认证方式的并发运行
class MultiAuthManager extends ChangeNotifier {
  // 单例实例
  static final MultiAuthManager _instance = MultiAuthManager._internal();
  static MultiAuthManager get instance => _instance;
  MultiAuthManager._internal();

  // 状态管理
  MultiAuthState _state = MultiAuthState.idle;
  MultiAuthState get state => _state;

  // 认证服务映射
  final Map<AuthMethod, AuthServiceInterface> _authServices = {};
  
  // 配置的认证方式（按优先级排序）
  List<AuthMethod> _enabledMethods = [];
  List<AuthMethod> get enabledMethods => List.unmodifiable(_enabledMethods);
  
  // 优先级管理器
  final AuthPriorityManager _priorityManager = AuthPriorityManager.instance;
  
  // 当前显示的认证方式（用于UI显示）
  AuthMethod? _currentDisplayMethod;
  AuthMethod? get currentDisplayMethod => _currentDisplayMethod;

  // 认证结果流
  final StreamController<AuthResult> _authResultController = 
      StreamController<AuthResult>.broadcast();
  Stream<AuthResult> get authResultStream => _authResultController.stream;

  // 错误信息
  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  // 当前认证的方式
  AuthMethod? _currentAuthMethod;
  AuthMethod? get currentAuthMethod => _currentAuthMethod;

  // 是否正在运行
  bool get isRunning => _state == MultiAuthState.listening || 
                       _state == MultiAuthState.authenticating;

  /// 初始化多认证管理器
  /// [context] BuildContext用于获取配置信息
  Future<void> initialize(BuildContext context) async {
    try {
      _setState(MultiAuthState.initializing);
      _clearError();

      // 获取配置信息
      final settingProvider = Provider.of<SettingProvider>(context, listen: false);
      _loadEnabledMethods(settingProvider);

      // 初始化认证服务
      await _initializeAuthServices(context);

      _setState(MultiAuthState.idle);
      print('多认证管理器初始化完成，启用的认证方式: $_enabledMethods');
    } catch (e) {
      _setError('初始化失败: $e');
      _setState(MultiAuthState.error);
    }
  }

  /// 启动所有认证方式监听
  Future<void> startListening() async {
    if (_state == MultiAuthState.listening || _state == MultiAuthState.authenticating) {
      print('认证监听已在运行中');
      return;
    }

    try {
      _setState(MultiAuthState.listening);
      _clearError();

      print('启动所有认证方式监听: $_enabledMethods');

      // 获取去重后的物理服务列表，避免重复启动
      final uniqueServices = <AuthServiceInterface>{};
      for (final method in _enabledMethods) {
        final service = _authServices[method];
        if (service != null) {
          uniqueServices.add(service);
        }
      }

      print('准备启动 ${uniqueServices.length} 个物理认证服务');

      // 依次启动每个物理服务
      final startedServices = <AuthServiceInterface>[];
      for (final service in uniqueServices) {
        try {
          if (!service.isListening) {
            await service.startListening();
            startedServices.add(service);
            
            // 获取使用此服务的认证方式名称
            final methodNames = _enabledMethods
                .where((method) => _authServices[method] == service)
                .map((method) => _getAuthMethodDisplayName(method))
                .join('、');
            
            print('$methodNames 认证服务启动成功');
          } else {
            print('服务已在监听中，跳过启动');
          }
        } catch (e) {
          print('启动认证服务失败: $e');
          // 记录失败但继续启动其他服务
          
          // 移除失败服务对应的认证方式
          final failedMethods = _enabledMethods
              .where((method) => _authServices[method] == service)
              .toList();
          
          for (final method in failedMethods) {
            _authServices.remove(method);
            _enabledMethods.remove(method);
          }
          
          print('已移除失败的认证方式: ${failedMethods.map((m) => _getAuthMethodDisplayName(m)).join('、')}');
        }
      }

      if (startedServices.isEmpty) {
        throw Exception('没有任何认证服务启动成功');
      }

      print('所有认证服务启动完成，成功启动 ${startedServices.length} 个服务');
      print('当前可用的认证方式: ${_enabledMethods.map((e) => _getAuthMethodDisplayName(e)).join('、')}');
    } catch (e) {
      _setError('启动认证监听失败: $e');
      _setState(MultiAuthState.error);
      throw e;
    }
  }

  /// 停止所有认证方式监听
  Future<void> stopListening({bool keepConnections = false}) async {
    if (_state != MultiAuthState.listening && _state != MultiAuthState.authenticating) {
      print('认证监听未在运行中');
      return;
    }

    try {
      print('停止所有认证方式监听${keepConnections ? '（保持连接）' : ''}');

      // 停止各个认证服务
      final stopTasks = <Future>[];
      
      for (final entry in _authServices.entries) {
        final method = entry.key;
        final service = entry.value;
        
        if (service.isListening) {
          print('停止${_getAuthMethodDisplayName(method)}认证监听');
          
          // 添加错误处理的停止任务
          stopTasks.add(_safeStopService(service, method, keepConnections));
        }
      }

      // 并行执行所有停止任务，但不等待全部完成以避免阻塞
      if (stopTasks.isNotEmpty) {
        try {
          await Future.wait(stopTasks).timeout(
            const Duration(seconds: 5),
            onTimeout: () {
              print('警告: 部分认证服务停止超时，强制继续');
              return [];
            },
          );
        } catch (e) {
          print('停止认证服务时出现错误: $e');
          // 继续执行，不要因为个别服务停止失败而阻塞整个流程
        }
      }

      _setState(MultiAuthState.idle);
      print('所有认证方式监听已停止${keepConnections ? '（设备连接保持）' : ''}');
    } catch (e) {
      print('停止认证监听时出现错误: $e');
      _setState(MultiAuthState.error);
      _setError('停止认证监听失败: $e');
    }
  }

  /// 安全地停止单个认证服务
  Future<void> _safeStopService(
    AuthServiceInterface service, 
    AuthMethod method, 
    bool keepConnections
  ) async {
    try {
      // 对于所有服务都使用stopListening方法
      // CardReaderService的stopListening已经实现了保持连接的逻辑
      await service.stopListening();
      print('${_getAuthMethodDisplayName(method)}认证服务监听已停止${keepConnections ? '（连接保持）' : ''}');
    } catch (e) {
      print('停止${_getAuthMethodDisplayName(method)}认证服务失败: $e');
      
      // 对于特定错误类型，进行特殊处理
      if (e.toString().contains('WebSocket') || 
          e.toString().contains('SocketException')) {
        print('检测到网络连接错误，忽略并继续');
      } else if (e.toString().contains('Failed to lookup symbol') || 
                 e.toString().contains('error code 127')) {
        print('检测到动态库符号查找失败，忽略并继续');
      } else {
        // 对于其他错误，记录但不阻塞流程
        print('未知错误类型，记录并继续: $e');
      }
    }
  }

  /// 处理认证结果
  void _handleAuthResult(AuthResult result) {
    if (_state != MultiAuthState.listening) {
      print('收到认证结果但当前状态不是监听状态: ${_state.name}');
      return;
    }

    print('多认证管理器: 收到认证结果: ${_getAuthMethodDisplayName(result.method)} - ${result.status.name}');

    // 立即更新状态和当前认证方式
    _setState(MultiAuthState.authenticating);
    _currentAuthMethod = result.method;
    
    // 切换显示方式到当前认证的方式
    switchDisplayMethod(result.method);

    // 如果认证成功，立即停止其他认证方式
    if (result.status == AuthStatus.success) {
      print('多认证管理器: 认证成功，立即停止其他认证方式');
      
      // 立即停止其他认证方式，不等待异步操作完成
      _stopOtherAuthMethodsImmediate(result.method);
      
      // 更新状态为已完成
      _setState(MultiAuthState.completed);
      
      // 广播认证结果
      _authResultController.add(result);
      
      // 延迟重启监听，给UI足够时间显示结果
      // 增加延迟时间，确保AuthFeedbackWidget有足够时间完成显示流程（3秒详细信息 + 5秒通行模式）
      Timer(const Duration(seconds: 10), () {
        if (_state == MultiAuthState.completed) {
          print('多认证管理器: 认证结果显示完成，重启所有认证监听并还原显示');
          restartListening();
          // 还原到默认显示方式
          restoreToDefaultDisplay();
        }
      });
      
    } else if (result.status == AuthStatus.failureNoMatch) {
      // 认证失败但没有匹配，显示失败信息并确保继续监听
      print('多认证管理器: 认证失败(无匹配): ${_getAuthMethodDisplayName(result.method)}，显示失败信息');
      
      // 广播失败结果，让UI显示失败信息
      _authResultController.add(result);
      
      // 短暂延迟后恢复到监听状态，但不重新启动服务
      Timer(const Duration(seconds: 3), () {
        if (_state == MultiAuthState.authenticating) {
          print('多认证管理器: 失败信息显示完成，恢复到监听状态');
          // 直接恢复到监听状态，不重新启动服务
          _setState(MultiAuthState.listening);
          // 还原到默认显示方式
          restoreToDefaultDisplay();
        }
      });
      
    } else if (result.status == AuthStatus.failureError) {
      // 认证错误，广播错误结果并重新启动监听
      print('多认证管理器: 认证错误: ${_getAuthMethodDisplayName(result.method)}，广播错误信息');
      
      // 广播错误结果
      _authResultController.add(result);
      
      // 短暂延迟后恢复到监听状态，但不重新启动服务
      Timer(const Duration(seconds: 3), () {
        if (_state == MultiAuthState.authenticating) {
          print('多认证管理器: 错误信息显示完成，恢复到监听状态');
          // 直接恢复到监听状态，不重新启动服务
          _setState(MultiAuthState.listening);
          // 还原到默认显示方式
          restoreToDefaultDisplay();
        }
      });
    }
  }

  /// 处理认证服务错误
  void _handleServiceError(AuthMethod method, String error) {
    print('认证服务错误 [${_getAuthMethodDisplayName(method)}]: $error');
    
    // 使用错误恢复管理器处理错误
    final serviceId = '${method.toString()}_auth_service';
    
    ErrorRecoveryManager.instance.handleServiceError(
      serviceId: serviceId,
      error: error,
      onRecovery: () async {
        // 尝试重新启动特定的认证服务
        await _recoverAuthService(method);
      },
    ).then((handled) {
      if (handled) {
        print('错误恢复管理器已处理${_getAuthMethodDisplayName(method)}的错误');
      } else {
        print('错误恢复管理器无法处理${_getAuthMethodDisplayName(method)}的错误，可能需要手动干预');
      }
    }).catchError((e) {
      print('错误恢复管理器处理${_getAuthMethodDisplayName(method)}错误时出现异常: $e');
    });
  }

  /// 恢复特定的认证服务
  Future<void> _recoverAuthService(AuthMethod method) async {
    try {
      print('尝试恢复认证服务: ${_getAuthMethodDisplayName(method)}');
      
      final service = _authServices[method];
      if (service == null) {
        print('认证服务不存在，无法恢复: ${_getAuthMethodDisplayName(method)}');
        return;
      }

      // 停止服务
      if (service.isListening) {
        await service.stopListening();
      }

      // 等待一段时间
      await Future.delayed(const Duration(seconds: 2));

      // 重新启动服务
      await service.startListening();
      
      print('认证服务恢复成功: ${_getAuthMethodDisplayName(method)}');
    } catch (e) {
      print('恢复认证服务失败: ${_getAuthMethodDisplayName(method)} - $e');
      throw e;
    }
  }

  /// 处理网络连接错误（保留原有方法以兼容）
  void _handleNetworkError(AuthMethod method, String error) {
    // 委托给错误恢复管理器处理
    _handleServiceError(method, error);
  }

  /// 处理动态库错误（保留原有方法以兼容）
  void _handleLibraryError(AuthMethod method, String error) {
    // 委托给错误恢复管理器处理
    _handleServiceError(method, error);
  }

  /// 处理读卡器错误（保留原有方法以兼容）
  void _handleReaderError(AuthMethod method, String error) {
    // 委托给错误恢复管理器处理
    _handleServiceError(method, error);
  }

  /// 处理通用错误（保留原有方法以兼容）
  void _handleGenericError(AuthMethod method, String error) {
    // 委托给错误恢复管理器处理
    _handleServiceError(method, error);
  }

  /// 安排网络重试（保留原有方法以兼容）
  void _scheduleNetworkRetry(AuthMethod method) {
    // 这个功能现在由错误恢复管理器处理
    print('网络重试功能已移至错误恢复管理器');
  }

  /// 立即停止其他认证方式（同步操作，不等待完成）
  void _stopOtherAuthMethodsImmediate(AuthMethod successMethod) {
    final otherServices = _authServices.entries
        .where((entry) => entry.key != successMethod);
    
    for (final entry in otherServices) {
      // 启动异步停止操作，但不等待完成
      entry.value.stopListening().then((_) {
        print('已停止 ${entry.key.name} 认证监听');
      }).catchError((e) {
        print('停止 ${entry.key.name} 认证监听失败: $e');
      });
    }
    
    print('已发起停止其他认证方式的操作');
  }

  /// 停止其他认证方式（异步等待完成）
  Future<void> _stopOtherAuthMethods(AuthMethod successMethod) async {
    final futures = _authServices.entries
        .where((entry) => entry.key != successMethod)
        .map((entry) async {
      try {
        await entry.value.stopListening();
        print('停止 ${entry.key.name} 认证监听');
      } catch (e) {
        print('停止 ${entry.key.name} 认证监听失败: $e');
      }
    });

    await Future.wait(futures);
    print('所有其他认证方式已停止');
  }

  /// 强制停止所有认证方式（紧急情况使用）
  Future<void> forceStopAllAuth() async {
    print('强制停止所有认证方式');
    
    _setState(MultiAuthState.idle);
    _currentAuthMethod = null;
    
    // 并发停止所有认证服务
    final futures = _authServices.values.map((service) async {
      try {
        await service.stopListening();
      } catch (e) {
        print('强制停止认证服务失败: $e');
      }
    });

    await Future.wait(futures);
    print('所有认证服务已强制停止');
  }

  /// 重新启动监听（用于刷新状态）
  Future<void> restartListening() async {
    print('多认证管理器: 重新启动认证监听');
    
    // 先暂停所有监听（保持连接）
    await stopListening();
    
    // 短暂延迟确保状态同步，但不需要太长因为连接保持
    await Future.delayed(const Duration(milliseconds: 200));
    
    // 重新启动监听（利用现有连接）
    await startListening();
    
    print('多认证管理器: 认证监听已重新启动');
  }

  /// 快速重启认证监听（认证失败后快速恢复）
  Future<void> _quickRestartListening() async {
    try {
      print('多认证管理器: 快速重启认证监听');
      
      // 检查各认证服务的状态并恢复监听
      for (final method in _enabledMethods) {
        final service = _authServices[method];
        if (service != null && service.isInitialized && !service.isListening) {
          try {
            await service.startListening();
            print('${_getAuthMethodDisplayName(method)}认证服务已恢复监听');
          } catch (e) {
            print('恢复${_getAuthMethodDisplayName(method)}认证监听失败: $e');
          }
        }
      }
      
      _setState(MultiAuthState.listening);
      print('多认证管理器: 快速重启完成');
    } catch (e) {
      print('快速重启认证监听失败，使用完整重启: $e');
      await restartListening();
    }
  }

  /// 重新启动监听（认证失败后）
  void _restartListeningAfterFailure() {
    print('多认证管理器: 认证失败后重新启动监听');
    // 使用快速重启以减少中断时间
    _quickRestartListening().catchError((e) {
      print('快速重启失败，尝试完整重启: $e');
      restartListening();
    });
  }

  /// 切换当前显示的认证方式
  /// [method] 要显示的认证方式
  void switchDisplayMethod(AuthMethod method) {
    if (_enabledMethods.contains(method) && _currentDisplayMethod != method) {
      print('多认证管理器: 切换显示方式 ${_currentDisplayMethod != null ? _getAuthMethodDisplayName(_currentDisplayMethod!) : '无'} -> ${_getAuthMethodDisplayName(method)}');
      _currentDisplayMethod = method;
      notifyListeners();
    }
  }

  /// 还原到默认显示的认证方式（最高优先级）
  void restoreToDefaultDisplay() {
    final primaryMethod = _priorityManager.primaryAuthMethod;
    if (primaryMethod != null && _currentDisplayMethod != primaryMethod) {
      print('多认证管理器: 还原到默认显示方式: ${_getAuthMethodDisplayName(primaryMethod)}');
      _currentDisplayMethod = primaryMethod;
      notifyListeners();
    }
  }

  /// 检查是否有多种认证方式
  bool get hasMultipleAuthMethods => _enabledMethods.length > 1;

  /// 加载启用的认证方式（使用优先级管理器）
  void _loadEnabledMethods(SettingProvider settingProvider) {
    _enabledMethods.clear();

    // 使用优先级管理器加载认证方式
    _priorityManager.loadAuthMethods(settingProvider);
    _enabledMethods = _priorityManager.orderedAuthMethods.toList();
    
    // 设置当前显示的认证方式为最高优先级的方式
    _currentDisplayMethod = _priorityManager.primaryAuthMethod;

    print('多认证管理器: 从优先级管理器加载的认证方式: ${_enabledMethods.map((e) => _getAuthMethodDisplayName(e)).join(' -> ')}');
    print('多认证管理器: 当前默认显示方式: ${_currentDisplayMethod != null ? _getAuthMethodDisplayName(_currentDisplayMethod!) : '无'}');
  }

  /// 初始化认证服务
  Future<void> _initializeAuthServices(BuildContext context) async {
    _authServices.clear();

    // 使用单个服务实例处理多种认证方式
    CardReaderService? sharedCardReaderService;
    QrScannerService? sharedQrScannerService;
    FaceAuthService? sharedFaceService;

    for (final method in _enabledMethods) {
      // 检查是否已经有相同类型的服务
      if (_authServices.containsKey(method)) {
        print('${_getAuthMethodDisplayName(method)} 认证服务已存在，跳过初始化');
        continue;
      }

      AuthServiceInterface? service;

      try {
        switch (method) {
          case AuthMethod.face:
            // 人脸识别服务
            if (sharedFaceService == null) {
              sharedFaceService = FaceAuthService();
              await sharedFaceService.initialize(context);
              print('初始化人脸认证服务');
            }
            service = sharedFaceService;
            break;
            
          case AuthMethod.idCard:
          case AuthMethod.readerCard:
          case AuthMethod.socialSecurityCard:
          case AuthMethod.citizenCard:
          case AuthMethod.eletricSocialSecurityCard:
            // 所有卡片类型使用同一个CardReaderService实例
            if (sharedCardReaderService == null) {
              sharedCardReaderService = CardReaderService();
              await sharedCardReaderService.initialize(context);
              print('初始化共享读卡器认证服务');
            }
            service = sharedCardReaderService;
            break;
            
          case AuthMethod.qrCode:
          case AuthMethod.wechatQRCode:
          case AuthMethod.wechatScanQRCode:
          case AuthMethod.alipayQRCode:
          case AuthMethod.aliCreditQRCode:
          case AuthMethod.huiwenQRCode:
          case AuthMethod.shangHaiQRCode:
          case AuthMethod.wechatOrAlipay:
          case AuthMethod.alipayQRCodeCredit:
            // 所有二维码类型使用同一个QrScannerService实例
            if (sharedQrScannerService == null) {
              sharedQrScannerService = QrScannerService();
              await sharedQrScannerService.initialize(context);
              print('初始化共享二维码扫描认证服务');
            }
            service = sharedQrScannerService;
            break;
            
          default:
            print('暂不支持的认证方式: ${_getAuthMethodDisplayName(method)}');
            continue;
        }

        if (service != null) {
          // 建立认证结果监听
          service.authResultStream.listen(_handleAuthResult);
          
          // 存储服务映射
          _authServices[method] = service;
          print('${_getAuthMethodDisplayName(method)} 认证服务初始化成功');
        }
      } catch (e) {
        print('初始化${_getAuthMethodDisplayName(method)}认证服务失败: $e');
        // 继续初始化其他服务，不因一个失败而中断
        continue;
      }
    }
    
    print('认证服务初始化完成，共初始化 ${_authServices.length} 种认证方式');
  }

  /// 映射AuthLoginType到AuthMethod
  AuthMethod? _mapAuthLoginTypeToAuthMethod(AuthLoginType type) {
    switch (type) {
      case AuthLoginType.faceAuth:
        return AuthMethod.face;
      case AuthLoginType.IDCard:
        return AuthMethod.idCard;
      case AuthLoginType.readerCard:
        return AuthMethod.readerCard;
      case AuthLoginType.socailSecurityCard:
        return AuthMethod.socialSecurityCard;
      case AuthLoginType.citizenCard:
        return AuthMethod.citizenCard;
      case AuthLoginType.eletricSocialSecurityCard:
        return AuthMethod.eletricSocialSecurityCard;
      case AuthLoginType.wechatQRCode:
        return AuthMethod.wechatQRCode;
      case AuthLoginType.wechatScanQRCode:
        return AuthMethod.wechatScanQRCode;
      case AuthLoginType.alipayQRCode:
        return AuthMethod.alipayQRCode;
      case AuthLoginType.aliCreditQRCode:
        return AuthMethod.aliCreditQRCode;
      case AuthLoginType.huiwenQRCode:
        return AuthMethod.huiwenQRCode;
      case AuthLoginType.shangHaiQRCode:
        return AuthMethod.shangHaiQRCode;
      case AuthLoginType.readerQRCode:
        return AuthMethod.qrCode;
      case AuthLoginType.keyboardInput:
        return AuthMethod.keyboardInput;
      case AuthLoginType.tencentTCard:
        return AuthMethod.tencentTCard;
      case AuthLoginType.IMIAuth:
        return AuthMethod.imiAuth;
      case AuthLoginType.takePhoto:
        return AuthMethod.takePhoto;
      case AuthLoginType.wecharOrAlipay:
        return AuthMethod.wechatOrAlipay;
      case AuthLoginType.alipayQRCode_credit:
        return AuthMethod.alipayQRCodeCredit;
      case AuthLoginType.jieYueBao:
        return AuthMethod.jieYueBao;
      default:
        return null;
    }
  }

  /// 设置状态
  void _setState(MultiAuthState newState) {
    if (_state != newState) {
      _state = newState;
      notifyListeners();
      print('多认证管理器状态变更: ${newState.name}');
    }
  }

  /// 设置错误
  void _setError(String error) {
    _errorMessage = error;
    print('多认证管理器错误: $error');
    notifyListeners();
  }

  /// 清除错误
  void _clearError() {
    _errorMessage = null;
  }

  /// 清理资源
  @override
  void dispose() {
    stopListening();
    _authResultController.close();
    
    // 清理所有认证服务
    for (final service in _authServices.values) {
      service.dispose();
    }
    _authServices.clear();

    super.dispose();
  }

  /// 获取指定认证方式的服务
  AuthServiceInterface? getAuthService(AuthMethod method) {
    return _authServices[method];
  }

  /// 检查指定认证方式是否已启用
  bool isMethodEnabled(AuthMethod method) {
    return _enabledMethods.contains(method);
  }

  /// 获取当前运行的认证服务数量
  int get activeServiceCount => _authServices.length;

  /// 获取认证方式的中文显示名称
  String _getAuthMethodDisplayName(AuthMethod method) {
    switch (method) {
      case AuthMethod.face:
        return '人脸识别';
      case AuthMethod.idCard:
        return '身份证';
      case AuthMethod.readerCard:
        return '读者证';
      case AuthMethod.qrCode:
        return '二维码';
      case AuthMethod.socialSecurityCard:
        return '社保卡';
      case AuthMethod.citizenCard:
        return '市民卡';
      case AuthMethod.eletricSocialSecurityCard:
        return '电子社保卡';
      case AuthMethod.wechatQRCode:
        return '微信二维码';
      case AuthMethod.wechatScanQRCode:
        return '微信扫码';
      case AuthMethod.alipayQRCode:
        return '支付宝二维码';
      case AuthMethod.aliCreditQRCode:
        return '芝麻信用码';
      case AuthMethod.huiwenQRCode:
        return '汇文二维码';
      case AuthMethod.shangHaiQRCode:
        return '上海随申码';
      case AuthMethod.keyboardInput:
        return '手动输入';
      case AuthMethod.tencentTCard:
        return '腾讯E证通';
      case AuthMethod.imiAuth:
        return 'IMI身份认证';
      case AuthMethod.takePhoto:
        return '拍照认证';
      case AuthMethod.wechatOrAlipay:
        return '微信/支付宝';
      case AuthMethod.alipayQRCodeCredit:
        return '支付宝信用认证';
      case AuthMethod.jieYueBao:
        return '借阅宝';
    }
  }

  /// 获取详细的服务状态信息
  Map<String, dynamic> getDetailedStatus() {
    final serviceStatus = <String, Map<String, dynamic>>{};
    
    for (final entry in _authServices.entries) {
      serviceStatus[entry.key.name] = entry.value.getStatus();
    }
    
    return {
      'manager_state': _state.name,
      'enabled_methods': _enabledMethods.map((e) => e.name).toList(),
      'current_auth_method': _currentAuthMethod?.name,
      'active_service_count': activeServiceCount,
      'error_message': _errorMessage,
      'services': serviceStatus,
    };
  }
} 